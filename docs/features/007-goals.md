# 🎯 Goals Feature Request

## 📋 Overview

This feature request outlines the implementation of a comprehensive financial goals management system for Finanze.Pro. The feature will enable users to create and manage their financial goals or targeted savings, connecting one or more accounts to track actual asset progress. Users can set target amounts and dates for their goals, providing motivation and structure for their financial planning.

## 🎯 Goals

- Provide users with tools to set and manage financial goals and savings targets
- Enable tracking of progress through connected accounts
- Support optional target amounts and target dates for goal completion
- Implement comprehensive CRUD operations for goals
- Allow visual customization with colors and icons
- Ensure seamless integration with existing accounts system
- Provide clear progress visualization and tracking

## 🔧 Technical Requirements

### API Integration

The feature will utilize existing API endpoints:

**Goals:**

- `GET /api/v1/goals` - List all financial goals
- `GET /api/v1/goals/{id}` - Get specific goal details
- `POST /api/v1/goals` - Create new goal
- `PUT /api/v1/goals/{id}` - Update goal
- `DELETE /api/v1/goals/{id}` - Delete goal

### Data Models

**Goal Types:**

```typescript
import type {
  Goal,
  GoalCreateRequest,
  GoalUpdateRequest,
  Currency,
  Decimal,
} from "@/api/types.gen";
```

The `Goal` type includes:

- `id: string` - Unique identifier
- `name: string` - Goal name
- `accounts: Array<string>` - Connected account IDs for tracking progress
- `targetAmount: Decimal & (string | null)` - Optional target amount
- `currentAmount: Decimal` - Current progress amount (calculated from accounts)
- `description: string | null` - Optional goal description
- `color: string | null` - Optional color for visual identification
- `iconUrl: string | null` - Optional icon URL for customization
- `targetDate: string | null` - Optional target completion date
- `currency: Currency` - Goal currency (inherited from accounts)
- `createdAt: string` - Creation timestamp
- `updatedAt: string` - Last update timestamp

## 🖥️ User Interface

### 1. Goals List Page (`/goals`)

**Layout:**

- Page header with title "🎯 Goals" and "Add Goal" button
- Goals cards grid showing all active goals
- Each goal card displays:
  - Goal name and description
  - Connected accounts information with count
  - Current progress vs target amount (if set)
  - Progress bar with percentage completion
  - Target date (if set) with days remaining indicator
  - Color indicator and icon (if set)
  - Quick actions: Edit, Delete
- Empty state when no goals exist

**Features:**

- Visual progress indicators with color-coded progress bars:
  - 🟢 Green: On track or ahead of schedule
  - 🟡 Yellow: Behind schedule but achievable
  - 🔴 Red: Significantly behind or overdue
- Currency formatting consistent with connected accounts
- Responsive grid layout adapting to screen sizes
- Search and filter functionality for large goal lists

### 2. Goal Details Page (`/goals/{id}`)

**Layout:**

- Goal header with name, description, and visual indicators (color/icon)
- Goal summary card showing:
  - Current amount and target amount
  - Connected accounts list with individual balances
  - Target date and progress timeline
  - Goal creation and last update dates
- Progress visualization section with detailed charts
- Connected accounts section with navigation links
- Quick actions: Edit Goal, Delete Goal

**Features:**

- Comprehensive goal progress tracking
- Individual account contribution breakdown
- Timeline visualization showing progress over time
- Account balance summaries with links to account details
- Currency conversion handling for multi-currency goals

### 3. Dialog Components

#### Goal Creation Dialog

**Form Fields:**

- Goal name (required, text input, max 100 characters)
- Description (optional, textarea, max 500 characters)
- Connected accounts (required, multi-select dropdown with search, min 1 account)
- Target amount (optional, decimal input with currency display)
- Target date (optional, date picker, future dates only)
- Color picker (optional, defaults to primary theme color)
- Icon URL (optional, text input for custom icon)

**Validation:**

- At least one account must be selected
- Target amount must be positive if provided
- Target date must be in the future if provided
- Account currency consistency validation

#### Goal Edit Dialog

**Form Fields:**

- Same as creation form, but with current values pre-filled
- All fields remain editable
- Additional warning when changing connected accounts

## 🔄 User Workflows

### Creating a Goal

1. User clicks "Add Goal" button on goals page
2. Goal creation dialog opens
3. User enters goal name and optional description
4. User selects one or more accounts to track progress
5. User optionally sets target amount and/or target date
6. User optionally customizes color and icon
7. User submits form
8. Goal is created and appears in goals list
9. Success toast notification is shown

### Tracking Goal Progress

1. User navigates to goals page to view all goals
2. Goal cards display current progress automatically calculated from connected accounts
3. Progress bars show completion percentage (current/target)
4. User can click on goal card to view detailed progress
5. Goal details page shows comprehensive tracking information
6. User can navigate to connected accounts for detailed view

### Managing Goals

1. User can edit goal settings from list or details view
2. User can update target amounts, dates, and connected accounts
3. User can delete goals (with confirmation dialog)
4. Changes are reflected immediately in progress calculations
5. Success notifications confirm all operations

## 🎨 Design Considerations

### Visual Hierarchy

- Clear progress indicators with intuitive color coding
- Prominent target and current amount display
- Goal status badges for quick identification
- Connected accounts summary with count indicators

### Responsive Design

- Mobile-first approach with touch-friendly interactions
- Optimized card layouts for different screen sizes
- Simplified mobile view focusing on essential information
- Accessible progress indicators and controls

### Accessibility

- Proper ARIA labels and roles for progress indicators
- Keyboard navigation support
- Screen reader friendly progress descriptions
- High contrast color options for progress states
- Clear visual distinction between goal states

## 🔗 Integration Points

### Navigation

- Add "Goals" link to main navigation header (positioned after "Budgets")
- Update dashboard to show goal progress widgets
- Add goal-related quick actions to account pages

### Account Integration

- Display goal associations on account detail pages
- Show which goals are connected to each account
- Account balance changes automatically update goal progress
- Multi-currency handling for goals spanning different currency accounts

### Dashboard Integration

- Goal progress widgets on main dashboard
- Upcoming target date notifications
- Goal achievement celebrations and notifications

## 📱 Implementation Phases

### Phase 1: Core Functionality

- Goals list page with basic card layout
- Goal creation and editing dialogs
- Basic goal details page
- CRUD operations with proper error handling
- Account selection and progress calculation

### Phase 2: Enhanced Features

- Advanced progress visualization
- Target date tracking and warnings
- Visual customization (colors and icons)
- Search and filtering capabilities

### Phase 3: Analytics & Integration

- Goal progress analytics and charts
- Dashboard widgets and notifications
- Achievement tracking and celebrations
- Enhanced mobile experience

## ✅ Acceptance Criteria

- [ ] Users can view all their financial goals in a clear, organized layout
- [ ] Users can create goals with one or more connected accounts
- [ ] Users can set optional target amounts and target dates
- [ ] Users can track progress automatically calculated from account balances
- [ ] Users can edit and delete goals with proper confirmations
- [ ] Users can customize goals with colors and icons
- [ ] Users can view detailed goal progress and account breakdowns
- [ ] All forms include proper validation and error handling
- [ ] Progress calculations work correctly across different currencies
- [ ] Interface is responsive and accessible across all devices
