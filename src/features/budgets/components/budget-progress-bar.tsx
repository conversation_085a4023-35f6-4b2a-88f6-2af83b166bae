import type { BudgetProgressStatus } from "../types";

import { cn } from "~/lib/utils";

import { getProgressColorClass } from "../utils";

interface Props {
  progress: BudgetProgressStatus;
  className?: string;
}

export default function BudgetProgressBar({ progress, className }: Props) {
  const { percentage, status } = progress;
  const colorClass = getProgressColorClass(status);

  return (
    <div className={cn("h-2 w-full rounded-lg bg-gray-200", className)}>
      <div
        className={cn("h-2 rounded-lg transition-all duration-300", colorClass, className)}
        style={{ width: `${Math.min(percentage, 100)}%` }}
      />
    </div>
  );
}
