import { useMemo } from "react";
import { useForm } from "react-hook-form";

import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";

import { zBudgetCreateRequest } from "~/api/zod.gen";
import { InputMultiSelect, InputRadioGroup, InputText, InputTextarea } from "~/components/inputs";
import { Button } from "~/components/ui/button";
import { Form } from "~/components/ui/form";
import { useAccounts } from "~/features/accounts/hooks";

import { budgetPeriodOptions, budgetTypeOptions, defaultBudgetPeriod, defaultBudgetType } from "../constants";
import { useBudgetActions } from "../hooks";

type FormData = z.infer<typeof zBudgetCreateRequest>;

interface Props {
  onSuccess?: () => void;
}

export default function BudgetFormCreate({ onSuccess }: Props) {
  const { accounts } = useAccounts();
  const { createBudgetMutation, closeBudgetDialog } = useBudgetActions();

  const accountOptions = useMemo(
    () =>
      accounts.map((account) => ({
        value: account.id,
        label: `${account.name} (${account.currency})`,
      })),
    [accounts]
  );

  const form = useForm<FormData>({
    resolver: zodResolver(zBudgetCreateRequest),
    defaultValues: {
      name: "",
      description: "",
      period: defaultBudgetPeriod,
      type: defaultBudgetType,
      value: "",
      accounts: [],
    },
  });

  const watchedType = form.watch("type");

  const handleSubmit = (data: FormData) => {
    createBudgetMutation(data);
    onSuccess?.();
    closeBudgetDialog();
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
        <InputText
          control={form.control}
          name="name"
          label="Budget Name"
          placeholder="Enter budget name"
          description="A descriptive name for your budget"
        />

        <InputTextarea
          control={form.control}
          name="description"
          label="Description"
          hint="Optional"
          placeholder="Enter budget description"
          description="Additional details about this budget"
          className="resize-none"
        />

        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <InputRadioGroup
            control={form.control}
            name="period"
            label="Budget Period"
            options={budgetPeriodOptions}
            description="How often this budget resets"
          />

          <InputRadioGroup
            control={form.control}
            name="type"
            label="Budget Type"
            options={budgetTypeOptions}
            description="Whether this is a fixed amount or percentage"
          />
        </div>

        <InputText
          control={form.control}
          name="value"
          label={watchedType === "percentage" ? "Percentage Value" : "Budget Amount"}
          type="number"
          step={watchedType === "percentage" ? "0.1" : "0.01"}
          placeholder={watchedType === "percentage" ? "0.0" : "0.00"}
          description={
            watchedType === "percentage"
              ? "Percentage of total income/balance to budget"
              : "Fixed amount to budget for this period"
          }
        />

        <InputMultiSelect
          control={form.control}
          name="accounts"
          label="Account Restrictions"
          hint="Optional"
          options={accountOptions}
          placeholder="Select accounts (optional)"
          description="Restrict this budget to specific accounts. Leave empty to include all accounts."
        />

        <div className="flex justify-between gap-3">
          <Button type="button" variant="outline" onClick={closeBudgetDialog} disabled={form.formState.isSubmitting}>
            Cancel
          </Button>
          <Button type="submit" disabled={form.formState.isSubmitting}>
            Create Budget
          </Button>
        </div>
      </form>
    </Form>
  );
}
