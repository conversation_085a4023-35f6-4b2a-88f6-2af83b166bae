import type { BudgetRecord } from "~/api/types.gen";

import { useState } from "react";

import { PaginationBlock } from "~/components/blocks";
import { LoadingIndicator } from "~/components/elements";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { useBaseCurrency } from "~/features/auth/hooks";
import { formatCurrency } from "~/lib/formatters";

import { useBudgetHistory } from "../hooks";
import { calculateBudgetProgress, getProgressTextColorClass } from "../utils";
import BudgetProgressBar from "./budget-progress-bar";

interface Props {
  budgetId: string;
}

export default function BudgetDetailsHistory({ budgetId }: Props) {
  const baseCurrency = useBaseCurrency();
  const [currentPage, setCurrentPage] = useState(1);
  const pageSize = 10;

  const { records, meta, isLoading, error } = useBudgetHistory({
    budgetId,
    page: currentPage,
    limit: pageSize,
  });

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Budget History</CardTitle>
        </CardHeader>
        <CardContent>
          <LoadingIndicator />
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Budget History</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-destructive">Failed to load budget history: {error.message}</p>
        </CardContent>
      </Card>
    );
  }

  const totalCount = meta?.total || 0;
  const totalPages = Math.ceil(totalCount / pageSize);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Budget History</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {records.length === 0 ? (
          <p className="text-muted-foreground py-8 text-center">No budget history records found.</p>
        ) : (
          <>
            <div className="space-y-4">
              {records.map((record: BudgetRecord) => {
                const progress = calculateBudgetProgress(record);

                return (
                  <div key={record.id} className="space-y-3 rounded-lg border p-4">
                    <div className="flex items-start justify-between">
                      <div>
                        <h4 className="font-medium">
                          {new Date(record.startDate).toLocaleDateString()} -{" "}
                          {new Date(record.endDate).toLocaleDateString()}
                        </h4>
                        <p className="text-muted-foreground text-sm">Budget Period</p>
                      </div>
                      <div className="text-right">
                        <p className={`font-medium ${getProgressTextColorClass(progress.status)}`}>
                          {progress.percentage}%
                        </p>
                        <p className="text-muted-foreground text-sm">Progress</p>
                      </div>
                    </div>

                    <BudgetProgressBar progress={progress} />

                    <div className="grid grid-cols-1 gap-4 text-sm md:grid-cols-3">
                      <div>
                        <h5 className="text-muted-foreground font-medium">Used Amount</h5>
                        <p>{formatCurrency(baseCurrency, record.usedAmount)}</p>
                      </div>

                      <div>
                        <h5 className="text-muted-foreground font-medium">Budget Amount</h5>
                        <p>{formatCurrency(baseCurrency, record.plannedAmount)}</p>
                      </div>

                      <div>
                        <h5 className="text-muted-foreground font-medium">Remaining</h5>
                        <p>
                          {formatCurrency(
                            baseCurrency,
                            (parseFloat(record.plannedAmount) - parseFloat(record.usedAmount)).toString()
                          )}
                        </p>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>

            {totalPages > 1 && (
              <div className="flex justify-center pt-4">
                <PaginationBlock currentPage={currentPage} totalPages={totalPages} onPageChange={handlePageChange} />
              </div>
            )}
          </>
        )}
      </CardContent>
    </Card>
  );
}
