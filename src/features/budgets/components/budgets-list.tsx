import type { Budget } from "../types";

import { useState } from "react";

import { PlusIcon } from "lucide-react";

import { LoadingIndicator } from "~/components/elements";
import { Button } from "~/components/ui/button";
import { Label } from "~/components/ui/label";
import { Switch } from "~/components/ui/switch";

import { useBudgetActions } from "../hooks";
import BudgetRow from "./budget-row";

interface Props {
  budgets: Budget[];
  activeBudgets: Budget[];
  archivedBudgets: Budget[];
  isLoading?: boolean;
}

export default function BudgetsList(props: Props) {
  const { budgets, activeBudgets, archivedBudgets, isLoading = false } = props;

  const { createBudget } = useBudgetActions();

  const [showArchived, setShowArchived] = useState(false);

  const displayedBudgets = showArchived ? budgets : activeBudgets;

  if (isLoading) {
    return <LoadingIndicator />;
  }

  return (
    <div className="space-y-6">
      {/* <PERSON><PERSON> controls */}
      <div className="flex flex-col items-start justify-between gap-4 sm:flex-row sm:items-center">
        <div className="flex items-center space-x-2">
          {archivedBudgets.length > 0 && (
            <>
              <Switch id="show-archived" checked={showArchived} onCheckedChange={setShowArchived} />
              <Label htmlFor="show-archived">Show archived budgets</Label>
              {showArchived && (
                <span className="text-muted-foreground text-sm">({archivedBudgets.length} archived)</span>
              )}
            </>
          )}
        </div>

        <Button onClick={createBudget}>
          <PlusIcon className="mr-2" />
          Add Budget
        </Button>
      </div>

      {/* Budgets grid */}
      {displayedBudgets.length === 0 ? (
        <div className="py-12 text-center">
          <p className="text-muted-foreground mb-4">
            {showArchived
              ? "No budgets found"
              : activeBudgets.length === 0 && archivedBudgets.length > 0
                ? "No active budgets. Toggle to show archived budgets."
                : "No budgets found"}
          </p>
          {!showArchived && activeBudgets.length === 0 && (
            <Button onClick={createBudget}>
              <PlusIcon className="mr-2 h-4 w-4" />
              Create your first budget
            </Button>
          )}
        </div>
      ) : (
        <div className="flex flex-col gap-6">
          {displayedBudgets.map((budget) => (
            <BudgetRow key={budget.id} budget={budget} />
          ))}
        </div>
      )}

      {/* Summary info */}
      {!showArchived && activeBudgets.length > 0 && archivedBudgets.length > 0 && (
        <div className="text-muted-foreground text-center text-sm">
          Showing {activeBudgets.length} active budget{activeBudgets.length !== 1 ? "s" : ""}.{" "}
          <button onClick={() => setShowArchived(true)} className="text-primary hover:underline">
            Show {archivedBudgets.length} archived budget{archivedBudgets.length !== 1 ? "s" : ""}
          </button>
        </div>
      )}
    </div>
  );
}
