import type { Budget } from "~/api/types.gen";

import { <PERSON> } from "@tanstack/react-router";
import { ArchiveIcon, EditIcon, MoreHorizontalIcon, ReceiptTextIcon, Trash2Icon } from "lucide-react";

import { Box, DefinitionBlock } from "~/components/blocks";
import { Button } from "~/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLinkItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "~/components/ui/dropdown-menu";
import { useBaseCurrency } from "~/features/auth/hooks";
import { formatCurrency } from "~/lib/formatters";

import { useBudgetActions } from "../hooks";
import { formatBudgetPeriod } from "../utils";

interface Props {
  budget: Budget;
}

export default function BudgetRow({ budget }: Props) {
  const baseCurrency = useBaseCurrency();

  const { editBudget, deleteBudget, archiveBudget } = useBudgetActions(budget);

  return (
    <Box className="flex items-center gap-2 ps-6">
      <div className="flex-grow space-y-0.5">
        <Link
          to="/budgets/$budgetId"
          params={{ budgetId: budget.id }}
          className="text-link hover:text-link/90 text-xl/6 font-semibold hover:underline"
        >
          {budget.name}
        </Link>
        {budget.description && <p className="text-gray text-sm">{budget.description}</p>}
      </div>

      <DefinitionBlock title="Period" size="md" className="min-w-32 gap-0">
        {formatBudgetPeriod(budget.period)}
      </DefinitionBlock>

      {budget.currentRecord && (
        <>
          <DefinitionBlock title="Planned" size="md" className="min-w-40 gap-0">
            {formatCurrency(baseCurrency, budget.currentRecord.plannedAmount)}
          </DefinitionBlock>
          <DefinitionBlock title="Used" size="md" className="min-w-40 gap-0">
            {formatCurrency(baseCurrency, budget.currentRecord.usedAmount)}
          </DefinitionBlock>
        </>
      )}

      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" size="icon">
            <MoreHorizontalIcon className="size-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuLinkItem to="/budgets/$budgetId" params={{ budgetId: budget.id }}>
            <ReceiptTextIcon className="mr-2 size-4" />
            View details
          </DropdownMenuLinkItem>
          <DropdownMenuItem onClick={editBudget}>
            <EditIcon className="mr-2 size-4" />
            Edit budget
          </DropdownMenuItem>
          <DropdownMenuItem onClick={archiveBudget}>
            <ArchiveIcon className="mr-2 size-4" />
            {budget.isArchived ? "Unarchive" : "Archive"}
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={deleteBudget} className="text-destructive">
            <Trash2Icon className="mr-2 size-4" />
            Delete
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </Box>
  );
}
