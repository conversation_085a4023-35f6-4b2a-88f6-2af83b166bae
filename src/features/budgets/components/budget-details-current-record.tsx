import type { Budget } from "~/api/types.gen";

import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { useBaseCurrency } from "~/features/auth/hooks";
import { formatCurrency, formatPercentage } from "~/lib/formatters";

import { calculateBudgetProgress, getProgressTextColorClass } from "../utils";
import BudgetProgressBar from "./budget-progress-bar";

interface Props {
  budget: Budget;
}

export default function BudgetDetailsCurrentRecord({ budget }: Props) {
  const baseCurrency = useBaseCurrency();
  const currentRecord = budget.currentRecord;
  const progress = calculateBudgetProgress(currentRecord);

  if (!currentRecord) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Current Period</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">No current period data available for this budget.</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Current Period</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center justify-between">
          <span className="text-lg font-medium">{formatCurrency(baseCurrency, currentRecord.usedAmount)} used</span>
          <span className={`text-lg font-medium ${getProgressTextColorClass(progress.status)}`}>
            {formatPercentage(progress.percentage.toFixed(2))}
          </span>
        </div>

        <BudgetProgressBar progress={progress} className="h-4" />

        <div className="grid grid-cols-1 gap-4 text-sm md:grid-cols-3">
          <div>
            <h4 className="text-muted-foreground font-medium">Used Amount</h4>
            <p className="text-lg">{formatCurrency(baseCurrency, currentRecord.usedAmount)}</p>
          </div>

          <div>
            <h4 className="text-muted-foreground font-medium">Budget Amount</h4>
            <p className="text-lg">{formatCurrency(baseCurrency, currentRecord.plannedAmount)}</p>
          </div>

          <div>
            <h4 className="text-muted-foreground font-medium">Remaining</h4>
            <p className="text-lg">
              {formatCurrency(
                baseCurrency,
                (parseFloat(currentRecord.plannedAmount) - parseFloat(currentRecord.usedAmount)).toString()
              )}
            </p>
          </div>
        </div>

        <div className="text-muted-foreground text-sm">
          <strong>Period:</strong> {new Date(currentRecord.startDate).toLocaleDateString()} -{" "}
          {new Date(currentRecord.endDate).toLocaleDateString()}
        </div>
      </CardContent>
    </Card>
  );
}
