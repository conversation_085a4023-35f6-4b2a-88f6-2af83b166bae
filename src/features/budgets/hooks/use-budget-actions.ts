import type { Budget } from "~/api/types.gen";

import { useConfirm } from "~/features/ui/confirmations/hooks";

import { useBudgetActionsStore } from "../store";
import { useCreateBudget } from "./use-create-budget";
import { useDeleteBudget } from "./use-delete-budget";
import { useUpdateBudget } from "./use-update-budget";

export function useBudgetActions(budget?: Budget) {
  const ask = useConfirm();

  // Store actions
  const { openCreateBudgetDialog, openEditBudgetDialog, closeBudgetDialog } = useBudgetActionsStore();

  // Mutation hooks
  const { createBudget: createBudgetMutation } = useCreateBudget();
  const { updateBudget: updateBudgetMutation } = useUpdateBudget();
  const { deleteBudget: deleteBudgetMutation } = useDeleteBudget();

  const createBudget = () => {
    openCreateBudgetDialog();
  };

  const editBudget = () => {
    if (!budget) return;
    openEditBudgetDialog(budget);
  };

  const deleteBudget = async () => {
    if (!budget) return;

    const confirmed = await ask({
      title: "Delete budget",
      description: `Are you sure you want to delete "${budget.name}"? This action cannot be undone.`,
      confirmText: "Yes, delete",
      cancelText: "Cancel",
      variant: "destructive",
    });

    if (confirmed) {
      deleteBudgetMutation(budget.id);
    }
  };

  const archiveBudget = async () => {
    if (!budget) return;

    const action = budget.isArchived ? "unarchive" : "archive";
    const confirmed = await ask({
      title: `${action.charAt(0).toUpperCase() + action.slice(1)} budget`,
      description: `Are you sure you want to ${action} "${budget.name}"?`,
      confirmText: `Yes, ${action}`,
      cancelText: "Cancel",
    });

    if (confirmed) {
      updateBudgetMutation(budget.id, { isArchived: !budget.isArchived });
    }
  };

  return {
    // Dialog actions
    createBudget,
    editBudget,
    deleteBudget,
    archiveBudget,
    closeBudgetDialog,

    // Direct mutation functions (for form submissions)
    createBudgetMutation,
    updateBudgetMutation,
    deleteBudgetMutation,
  };
}
