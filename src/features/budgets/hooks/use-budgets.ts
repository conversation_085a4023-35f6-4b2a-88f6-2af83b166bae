import { useMemo } from "react";

import { useQuery } from "@tanstack/react-query";

import { listBudgetsOptions } from "~/api/@tanstack/react-query.gen";

import { filterBudgetsByArchived } from "../utils";

interface UseBudgetsOptions {
  includeArchived?: boolean;
}

export function useBudgets(options: UseBudgetsOptions = {}) {
  const { includeArchived = false } = options;

  const { data: budgetsData, isLoading, error, refetch } = useQuery(listBudgetsOptions({ query: { includeArchived } }));

  const budgets = useMemo(() => budgetsData ?? [], [budgetsData]);
  const activeBudgets = useMemo(() => filterBudgetsByArchived(budgets, false), [budgets]);
  const archivedBudgets = useMemo(() => filterBudgetsByArchived(budgets, true), [budgets]);

  return { budgets, activeBudgets, archivedBudgets, isLoading, error, refetch };
}
