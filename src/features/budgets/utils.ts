import type { Budget, BudgetProgressStatus, BudgetRecord } from "./types";

import { PROGRESS_THRESHOLDS } from "./constants";

/**
 * Calculate budget progress status based on used and planned amounts
 */
export function calculateBudgetProgress(record?: BudgetRecord): BudgetProgressStatus {
  const usedAmount = parseFloat(record?.usedAmount || "0");
  const plannedAmount = parseFloat(record?.plannedAmount || "0");

  if (plannedAmount === 0) {
    return { percentage: 0, status: "under" };
  }

  const percentage = Math.round((usedAmount / plannedAmount) * 100);

  let status: BudgetProgressStatus["status"];
  if (percentage >= PROGRESS_THRESHOLDS.OVER) {
    status = "over";
  } else if (percentage >= PROGRESS_THRESHOLDS.WARNING) {
    status = "warning";
  } else {
    status = "under";
  }

  return { percentage, status };
}

/**
 * Filter budgets by archived status
 */
export function filterBudgetsByArchived(budgets: Budget[], includeArchived: boolean): Budget[] {
  return includeArchived ? budgets : budgets.filter((budget) => !budget.isArchived);
}

/**
 * Format budget period for display
 */
export function formatBudgetPeriod(period: Budget["period"]): string {
  const periodMap = {
    week: "Weekly",
    month: "Monthly",
    quarter: "Quarterly",
    year: "Yearly",
  };
  return periodMap[period];
}

/**
 * Format budget type for display
 */
export function formatBudgetType(type: Budget["type"]): string {
  const typeMap = {
    fixed: "Fixed Amount",
    percentage: "Percentage",
  };
  return typeMap[type];
}

/**
 * Get progress color class based on status
 */
export function getProgressColorClass(status: BudgetProgressStatus["status"]): string {
  const colorMap = {
    under: "bg-emerald-500",
    warning: "bg-orange-500",
    over: "bg-rose-500",
  };
  return colorMap[status];
}

/**
 * Get progress text color class based on status
 */
export function getProgressTextColorClass(status: BudgetProgressStatus["status"]): string {
  const colorMap = {
    under: "text-emerald-600",
    warning: "text-orange-600",
    over: "text-rose-600",
  };
  return colorMap[status];
}
