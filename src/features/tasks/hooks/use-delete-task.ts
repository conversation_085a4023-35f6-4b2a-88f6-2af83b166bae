import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

import { deleteTaskMutation, getTaskQueryKey, listTasksQueryKey } from "~/api/@tanstack/react-query.gen";

export function useDeleteTask() {
  const queryClient = useQueryClient();

  const mutation = useMutation({
    ...deleteTaskMutation(),
    onSuccess: (_, variables) => {
      // Invalidate tasks list to refetch
      void queryClient.invalidateQueries({
        queryKey: listTasksQueryKey(),
      });

      // Remove specific task query from cache
      queryClient.removeQueries({
        queryKey: getTaskQueryKey({ path: { id: variables.path.id } }),
      });

      toast.success("Task deleted successfully");
    },
    onError: (error) => {
      console.error("Failed to delete task:", error);
      toast.error("Failed to delete task");
    },
  });

  const deleteTaskFn = (taskId: string) => {
    mutation.mutate({ path: { id: taskId } });
  };

  return {
    deleteTask: deleteTaskFn,
    isLoading: mutation.isPending,
    error: mutation.error,
  };
}
