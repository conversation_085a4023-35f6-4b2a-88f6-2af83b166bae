import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

import { deleteTask } from "~/api/sdk.gen";

export function useDeleteTask() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (taskId: string) =>
      deleteTask({
        path: { id: taskId },
      }),
    onSuccess: () => {
      // Invalidate tasks list to refetch
      void queryClient.invalidateQueries({ queryKey: ["listTasks"] });
      toast.success("Task deleted successfully");
    },
    onError: (error) => {
      console.error("Failed to delete task:", error);
      toast.error("Failed to delete task");
    },
  });
}
