import { useQuery } from "@tanstack/react-query";

import { getTaskHistoryOptions } from "~/api/@tanstack/react-query.gen";

interface UseTaskHistoryOptions {
  page?: number;
  pageSize?: number;
}

export function useTaskHistory(taskId: string, options: UseTaskHistoryOptions = {}) {
  const { page = 1, pageSize = 20 } = options;

  const action = useQuery(getTaskHistoryOptions({ path: { id: taskId }, query: { page, limit: pageSize } }));
  const { data: historyData, isLoading, error, refetch } = action;

  return { history: historyData?.items ?? [], meta: historyData?.meta, isLoading, error, refetch };
}
