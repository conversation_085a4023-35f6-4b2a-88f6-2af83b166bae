import { useCallback } from "react";

import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

import {
  assignTaskTransactionsMutation,
  getTaskHistoryQueryKey,
  getTaskQuery<PERSON>ey,
  listTasksQuery<PERSON>ey,
  removeTaskTransactionsMutation,
  replaceTaskTransactionsMutation,
} from "~/api/@tanstack/react-query.gen";

import { useTaskActionsStore } from "../store";

export function useTaskTransactionActions() {
  const { openTransactionDialog, closeTransactionDialog } = useTaskActionsStore();
  const queryClient = useQueryClient();

  const assignTransactionsMutation = useMutation({
    ...assignTaskTransactionsMutation(),
    onSuccess: (_, variables) => {
      void queryClient.invalidateQueries({
        queryKey: getTaskQueryKey({ path: { id: variables.path.id } }),
      });
      void queryClient.invalidateQueries({
        queryKey: getTaskHistoryQueryKey({ path: { id: variables.path.id } }),
      });
      void queryClient.invalidateQueries({
        queryKey: listTasksQueryKey(),
      });
      toast.success("Transactions assigned successfully");
    },
    onError: (error) => {
      console.error("Failed to assign transactions:", error);
      toast.error("Failed to assign transactions");
    },
  });

  const removeTransactionsMutation = useMutation({
    ...removeTaskTransactionsMutation(),
    onSuccess: (_, variables) => {
      void queryClient.invalidateQueries({
        queryKey: getTaskQueryKey({ path: { id: variables.path.id } }),
      });
      void queryClient.invalidateQueries({
        queryKey: getTaskHistoryQueryKey({ path: { id: variables.path.id } }),
      });
      void queryClient.invalidateQueries({
        queryKey: listTasksQueryKey(),
      });
      toast.success("Transactions removed successfully");
    },
    onError: (error) => {
      console.error("Failed to remove transactions:", error);
      toast.error("Failed to remove transactions");
    },
  });

  const replaceTransactionsMutation = useMutation({
    ...replaceTaskTransactionsMutation(),
    onSuccess: (_, variables) => {
      void queryClient.invalidateQueries({
        queryKey: getTaskQueryKey({ path: { id: variables.path.id } }),
      });
      void queryClient.invalidateQueries({
        queryKey: getTaskHistoryQueryKey({ path: { id: variables.path.id } }),
      });
      void queryClient.invalidateQueries({
        queryKey: listTasksQueryKey(),
      });
      toast.success("Transactions updated successfully");
    },
    onError: (error) => {
      console.error("Failed to update transactions:", error);
      toast.error("Failed to update transactions");
    },
  });

  const openTransactionManager = useCallback(
    (taskId: string) => {
      openTransactionDialog(taskId);
    },
    [openTransactionDialog]
  );

  const assignTransactions = (taskId: string, transactionIds: string[]) => {
    assignTransactionsMutation.mutate({
      path: { id: taskId },
      body: { transactions: transactionIds },
    });
  };

  const removeTransactions = (taskId: string, transactionIds: string[]) => {
    removeTransactionsMutation.mutate({
      path: { id: taskId },
      body: { transactions: transactionIds },
    });
  };

  const replaceTransactions = (taskId: string, transactionIds: string[]) => {
    replaceTransactionsMutation.mutate({
      path: { id: taskId },
      body: { transactions: transactionIds },
    });
  };

  return {
    openTransactionManager,
    closeTransactionDialog,
    assignTransactions,
    removeTransactions,
    replaceTransactions,
    isAssigning: assignTransactionsMutation.isPending,
    isRemoving: removeTransactionsMutation.isPending,
    isReplacing: replaceTransactionsMutation.isPending,
  };
}
