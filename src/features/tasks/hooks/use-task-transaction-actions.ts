import type { TaskTransactionAssignment } from "~/api/types.gen";

import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

import { assignTaskTransactions, removeTaskTransactions, replaceTaskTransactions } from "~/api/sdk.gen";

import { useTaskActionsStore } from "../store";

export function useTaskTransactionActions() {
  const { openTransactionDialog, closeTransactionDialog } = useTaskActionsStore((state) => ({
    openTransactionDialog: state.openTransactionDialog,
    closeTransactionDialog: state.closeTransactionDialog,
  }));
  const queryClient = useQueryClient();

  const assignTransactionsMutation = useMutation({
    mutationFn: ({ taskId, data }: { taskId: string; data: TaskTransactionAssignment }) =>
      assignTaskTransactions({
        path: { id: taskId },
        body: data,
      }),
    onSuccess: (_, { taskId }) => {
      void queryClient.invalidateQueries({ queryKey: ["getTask", taskId] });
      void queryClient.invalidateQueries({ queryKey: ["listTasks"] });
      toast.success("Transactions assigned successfully");
    },
    onError: (error) => {
      console.error("Failed to assign transactions:", error);
      toast.error("Failed to assign transactions");
    },
  });

  const removeTransactionsMutation = useMutation({
    mutationFn: ({ taskId, data }: { taskId: string; data: TaskTransactionAssignment }) =>
      removeTaskTransactions({
        path: { id: taskId },
        body: data,
      }),
    onSuccess: (_, { taskId }) => {
      void queryClient.invalidateQueries({ queryKey: ["getTask", taskId] });
      void queryClient.invalidateQueries({ queryKey: ["listTasks"] });
      toast.success("Transactions removed successfully");
    },
    onError: (error) => {
      console.error("Failed to remove transactions:", error);
      toast.error("Failed to remove transactions");
    },
  });

  const replaceTransactionsMutation = useMutation({
    mutationFn: ({ taskId, data }: { taskId: string; data: TaskTransactionAssignment }) =>
      replaceTaskTransactions({
        path: { id: taskId },
        body: data,
      }),
    onSuccess: (_, { taskId }) => {
      void queryClient.invalidateQueries({ queryKey: ["getTask", taskId] });
      void queryClient.invalidateQueries({ queryKey: ["listTasks"] });
      toast.success("Transactions updated successfully");
    },
    onError: (error) => {
      console.error("Failed to update transactions:", error);
      toast.error("Failed to update transactions");
    },
  });

  const openTransactionManager = (taskId: string) => {
    openTransactionDialog(taskId);
  };

  const assignTransactions = (taskId: string, transactionIds: string[]) => {
    assignTransactionsMutation.mutate({
      taskId,
      data: { transactions: transactionIds },
    });
  };

  const removeTransactions = (taskId: string, transactionIds: string[]) => {
    removeTransactionsMutation.mutate({
      taskId,
      data: { transactions: transactionIds },
    });
  };

  const replaceTransactions = (taskId: string, transactionIds: string[]) => {
    replaceTransactionsMutation.mutate({
      taskId,
      data: { transactions: transactionIds },
    });
  };

  return {
    openTransactionManager,
    closeTransactionDialog,
    assignTransactions,
    removeTransactions,
    replaceTransactions,
    isAssigning: assignTransactionsMutation.isPending,
    isRemoving: removeTransactionsMutation.isPending,
    isReplacing: replaceTransactionsMutation.isPending,
  };
}
