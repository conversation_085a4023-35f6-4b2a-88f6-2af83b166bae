import { useMemo } from "react";

import { useQuery } from "@tanstack/react-query";

import { listTasksOptions } from "~/api/@tanstack/react-query.gen";

import { filterTasksByStatus, sortTasks } from "../utils";
import type { TaskFilterOptions, TaskSortOptions } from "../types";

interface UseTasksOptions extends TaskFilterOptions, TaskSortOptions {
  page?: number;
  pageSize?: number;
}

export function useTasks(options: UseTasksOptions = {}) {
  const {
    status = "all",
    period = "all",
    includeArchived = false,
    sortBy = "dueDate",
    sortOrder = "asc",
    page = 1,
    pageSize = 20,
  } = options;

  const { data: tasksData, isLoading, error, refetch } = useQuery(
    listTasksOptions({
      query: {
        page,
        pageSize,
        includeArchived,
      },
    })
  );

  const tasks = useMemo(() => {
    let processedTasks = tasksData?.items ?? [];

    // Filter by status
    if (status !== "all") {
      processedTasks = filterTasksByStatus(processedTasks, status);
    }

    // Filter by period
    if (period !== "all") {
      processedTasks = processedTasks.filter((task) => task.period === period);
    }

    // Sort tasks
    processedTasks = sortTasks(processedTasks, { sortBy, sortOrder });

    return processedTasks;
  }, [tasksData?.items, status, period, sortBy, sortOrder]);

  const activeTasks = useMemo(
    () => filterTasksByStatus(tasksData?.items ?? [], "active"),
    [tasksData?.items]
  );

  const completedTasks = useMemo(
    () => filterTasksByStatus(tasksData?.items ?? [], "completed"),
    [tasksData?.items]
  );

  const overdueTasks = useMemo(
    () => filterTasksByStatus(tasksData?.items ?? [], "overdue"),
    [tasksData?.items]
  );

  return {
    tasks,
    activeTasks,
    completedTasks,
    overdueTasks,
    meta: tasksData?.meta,
    isLoading,
    error,
    refetch,
  };
}
