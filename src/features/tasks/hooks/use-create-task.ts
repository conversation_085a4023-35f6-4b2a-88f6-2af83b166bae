import type { TaskCreateRequest } from "~/api/types.gen";

import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

import { createTaskMutation, listTasksQueryKey } from "~/api/@tanstack/react-query.gen";

export function useCreateTask() {
  const queryClient = useQueryClient();

  const mutation = useMutation({
    ...createTaskMutation(),
    onSuccess: () => {
      // Invalidate tasks list to refetch
      void queryClient.invalidateQueries({
        queryKey: listTasksQueryKey(),
      });
      toast.success("Task created successfully");
    },
    onError: (error) => {
      console.error("Failed to create task:", error);
      toast.error("Failed to create task");
    },
  });

  const createTaskFn = (data: TaskCreateRequest) => {
    mutation.mutate({ body: data });
  };

  return {
    createTask: createTaskFn,
    isLoading: mutation.isPending,
    error: mutation.error,
  };
}
