import type { TaskCreateRequest } from "~/api/types.gen";

import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

import { createTask } from "~/api/sdk.gen";

export function useCreateTask() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: TaskCreateRequest) =>
      createTask({
        body: data,
      }),
    onSuccess: () => {
      // Invalidate tasks list to refetch
      void queryClient.invalidateQueries({ queryKey: ["listTasks"] });
      toast.success("Task created successfully");
    },
    onError: (error) => {
      console.error("Failed to create task:", error);
      toast.error("Failed to create task");
    },
  });
}
