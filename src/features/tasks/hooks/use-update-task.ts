import type { TaskStatusUpdateRequest, TaskUpdateRequest } from "~/api/types.gen";

import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

import { updateTask, updateTaskStatus } from "~/api/sdk.gen";

export function useUpdateTask() {
  const queryClient = useQueryClient();

  const mutation = useMutation({
    mutationFn: ({ taskId, data }: { taskId: string; data: TaskUpdateRequest }) =>
      updateTask({
        path: { id: taskId },
        body: data,
      }),
    onSuccess: (_, { taskId }) => {
      // Invalidate tasks list and specific task to refetch
      void queryClient.invalidateQueries({ queryKey: ["listTasks"] });
      void queryClient.invalidateQueries({ queryKey: ["getTask", taskId] });
      toast.success("Task updated successfully");
    },
    onError: (error) => {
      console.error("Failed to update task:", error);
      toast.error("Failed to update task");
    },
  });

  const updateTaskFn = (taskId: string, data: TaskUpdateRequest) => {
    mutation.mutate({ taskId, data });
  };

  return {
    updateTask: updateTaskFn,
    isLoading: mutation.isPending,
    error: mutation.error,
  };
}

export function useUpdateTaskStatus() {
  const queryClient = useQueryClient();

  const mutation = useMutation({
    mutationFn: ({ taskId, data }: { taskId: string; data: TaskStatusUpdateRequest }) =>
      updateTaskStatus({
        path: { id: taskId },
        body: data,
      }),
    onSuccess: (_, { taskId }) => {
      // Invalidate tasks list and specific task to refetch
      void queryClient.invalidateQueries({ queryKey: ["listTasks"] });
      void queryClient.invalidateQueries({ queryKey: ["getTask", taskId] });
      void queryClient.invalidateQueries({ queryKey: ["getTaskHistory", taskId] });
      toast.success("Task status updated successfully");
    },
    onError: (error) => {
      console.error("Failed to update task status:", error);
      toast.error("Failed to update task status");
    },
  });

  const updateTaskStatusFn = (taskId: string, data: TaskStatusUpdateRequest) => {
    mutation.mutate({ taskId, data });
  };

  return {
    updateTaskStatus: updateTaskStatusFn,
    isLoading: mutation.isPending,
    error: mutation.error,
  };
}
