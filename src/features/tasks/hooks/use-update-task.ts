import type { TaskStatusUpdateRequest, TaskUpdateRequest } from "~/api/types.gen";

import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

import {
  getTaskHistoryQuery<PERSON>ey,
  getTask<PERSON><PERSON><PERSON><PERSON>ey,
  listTasksQueryKey,
  updateTaskMutation,
  updateTaskStatusMutation,
} from "~/api/@tanstack/react-query.gen";

export function useUpdateTask() {
  const queryClient = useQueryClient();

  const mutation = useMutation({
    ...updateTaskMutation(),
    onSuccess: (_, variables) => {
      // Invalidate tasks list and specific task to refetch
      void queryClient.invalidateQueries({
        queryKey: listTasksQueryKey(),
      });
      void queryClient.invalidateQueries({
        queryKey: getTaskQueryKey({ path: { id: variables.path.id } }),
      });
      toast.success("Task updated successfully");
    },
    onError: (error) => {
      console.error("Failed to update task:", error);
      toast.error("Failed to update task");
    },
  });

  const updateTaskFn = (taskId: string, data: TaskUpdateRequest) => {
    mutation.mutate({ path: { id: taskId }, body: data });
  };

  return {
    updateTask: updateTaskFn,
    isLoading: mutation.isPending,
    error: mutation.error,
  };
}

export function useUpdateTaskStatus() {
  const queryClient = useQueryClient();

  const mutation = useMutation({
    ...updateTaskStatusMutation(),
    onSuccess: (_, variables) => {
      // Invalidate tasks list and specific task to refetch
      void queryClient.invalidateQueries({
        queryKey: listTasksQueryKey(),
      });
      void queryClient.invalidateQueries({
        queryKey: getTaskQueryKey({ path: { id: variables.path.id } }),
      });
      void queryClient.invalidateQueries({
        queryKey: getTaskHistoryQueryKey({ path: { id: variables.path.id } }),
      });
      toast.success("Task status updated successfully");
    },
    onError: (error) => {
      console.error("Failed to update task status:", error);
      toast.error("Failed to update task status");
    },
  });

  const updateTaskStatusFn = (taskId: string, data: TaskStatusUpdateRequest) => {
    mutation.mutate({ path: { id: taskId }, body: data });
  };

  return {
    updateTaskStatus: updateTaskStatusFn,
    isLoading: mutation.isPending,
    error: mutation.error,
  };
}
