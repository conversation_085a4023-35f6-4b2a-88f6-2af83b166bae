import type { TaskResponse } from "~/api/types.gen";

import { useConfirm } from "~/features/ui/confirmations/hooks";

import { useTaskActionsStore } from "../store";
import { useCreateTask } from "./use-create-task";
import { useDeleteTask } from "./use-delete-task";
import { useUpdateTask, useUpdateTaskStatus } from "./use-update-task";

export function useTaskActions(task?: TaskResponse) {
  const ask = useConfirm();

  // Store actions
  const { openCreateTaskDialog, openEditTaskDialog, closeTaskDialog } = useTaskActionsStore();

  // Mutation hooks
  const { createTask: createTaskMutation, isLoading: isCreating } = useCreateTask();
  const { updateTask: updateTaskMutation, isLoading: isUpdating } = useUpdateTask();
  const { deleteTask: deleteTaskMutation, isLoading: isDeleting } = useDeleteTask();
  const { updateTaskStatus: updateTaskStatusMutation, isLoading: isUpdatingStatus } = useUpdateTaskStatus();

  const createTask = () => {
    openCreateTaskDialog();
  };

  const editTask = () => {
    if (!task) return;
    openEditTaskDialog(task);
  };

  const deleteTask = async () => {
    if (!task) return;

    const confirmed = await ask({
      title: "Delete task",
      description: `Are you sure you want to delete "${task.title}"? This action cannot be undone.`,
      confirmText: "Yes, delete",
      cancelText: "Cancel",
      variant: "destructive",
    });

    if (confirmed) {
      deleteTaskMutation(task.id);
    }
  };

  const archiveTask = async () => {
    if (!task) return;

    const action = task.isArchived ? "unarchive" : "archive";
    const confirmed = await ask({
      title: `${action.charAt(0).toUpperCase() + action.slice(1)} task`,
      description: `Are you sure you want to ${action} "${task.title}"?`,
      confirmText: `Yes, ${action}`,
      cancelText: "Cancel",
    });

    if (confirmed) {
      updateTaskMutation(task.id, { isArchived: !task.isArchived });
    }
  };

  const markCompleted = (amount?: string) => {
    if (!task) return;

    updateTaskStatusMutation(task.id, {
      status: "completed",
      amount: amount || undefined,
    });
  };

  const markSkipped = () => {
    if (!task) return;

    updateTaskStatusMutation(task.id, {
      status: "skipped",
      amount: undefined,
    });
  };

  return {
    // Dialog actions
    createTask,
    editTask,
    deleteTask,
    archiveTask,
    closeTaskDialog,

    // Status actions
    markCompleted,
    markSkipped,

    // Loading states
    isCreating,
    isUpdating,
    isDeleting,
    isUpdatingStatus,

    // Direct mutation functions (for form submissions)
    createTaskMutation,
    updateTaskMutation,
    deleteTaskMutation,
    updateTaskStatusMutation,
  };
}
