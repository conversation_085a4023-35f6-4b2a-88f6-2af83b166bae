import type { TaskResponse } from "~/api/types.gen";

import { useConfirm } from "~/features/ui/confirmations/hooks";

import { useTaskActionsStore } from "../store";
import { useDeleteTask } from "./use-delete-task";
import { useUpdateTaskStatus } from "./use-update-task";

/**
 * Hook that provides task action functions for UI interactions.
 * This is a convenience hook that wraps the store actions and mutations.
 */
export function useTaskActions() {
  const confirm = useConfirm();
  const deleteTaskMutation = useDeleteTask();
  const updateTaskStatusMutation = useUpdateTaskStatus();

  const { openCreateTaskDialog, openEditTaskDialog, closeTaskDialog } = useTaskActionsStore();

  const createTask = () => {
    openCreateTaskDialog();
  };

  const editTask = (task: TaskResponse) => {
    openEditTaskDialog(task);
  };

  const deleteTask = async (task: TaskResponse) => {
    const confirmed = await confirm({
      title: "Delete Task",
      description: `Are you sure you want to delete "${task.title}"? This action cannot be undone.`,
      confirmText: "Delete",
      variant: "destructive",
    });

    if (confirmed) {
      deleteTaskMutation.mutate(task.id);
    }
  };

  const markCompleted = (task: TaskResponse, amount?: string) => {
    updateTaskStatusMutation.mutate({
      taskId: task.id,
      data: {
        status: "completed",
        amount: amount || undefined,
      },
    });
  };

  const markSkipped = (task: TaskResponse) => {
    updateTaskStatusMutation.mutate({
      taskId: task.id,
      data: {
        status: "skipped",
      },
    });
  };

  return {
    createTask,
    editTask,
    deleteTask,
    markCompleted,
    markSkipped,
    closeTaskDialog,
    isDeleting: deleteTaskMutation.isPending,
    isUpdatingStatus: updateTaskStatusMutation.isPending,
  };
}
