import type { TaskResponse, TaskStatus, TaskPeriod } from "~/api/types.gen";
import type { TaskStatusInfo, TaskSortOptions, TaskDisplayData } from "./types";
import { TASK_PERIOD_CONFIGS } from "./constants";

/**
 * Calculate task status information including overdue status
 */
export function calculateTaskStatusInfo(task: TaskResponse): TaskStatusInfo {
  const current = task.current;
  
  if (!current) {
    return {
      status: "active",
      isOverdue: false,
      daysUntilDue: null,
      daysOverdue: null,
    };
  }

  const now = new Date();
  const endDate = new Date(current.endDate);
  const diffTime = endDate.getTime() - now.getTime();
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  const isOverdue = current.status === "active" && diffDays < 0;
  const daysUntilDue = current.status === "active" && diffDays >= 0 ? diffDays : null;
  const daysOverdue = isOverdue ? Math.abs(diffDays) : null;

  return {
    status: current.status,
    isOverdue,
    daysUntilDue,
    daysOverdue,
  };
}

/**
 * Filter tasks by status including overdue detection
 */
export function filterTasksByStatus(tasks: TaskResponse[], status: TaskStatus | "overdue"): TaskResponse[] {
  return tasks.filter((task) => {
    const statusInfo = calculateTaskStatusInfo(task);
    
    if (status === "overdue") {
      return statusInfo.isOverdue;
    }
    
    return statusInfo.status === status;
  });
}

/**
 * Sort tasks by various criteria
 */
export function sortTasks(tasks: TaskResponse[], options: TaskSortOptions): TaskResponse[] {
  const { sortBy, sortOrder } = options;
  
  return [...tasks].sort((a, b) => {
    let comparison = 0;
    
    switch (sortBy) {
      case "title":
        comparison = a.title.localeCompare(b.title);
        break;
      case "period":
        const periodOrder = ["weekly", "monthly", "quarterly", "yearly"];
        comparison = periodOrder.indexOf(a.period) - periodOrder.indexOf(b.period);
        break;
      case "dueDate":
        const aStatusInfo = calculateTaskStatusInfo(a);
        const bStatusInfo = calculateTaskStatusInfo(b);
        
        // Overdue tasks first, then by days until due
        if (aStatusInfo.isOverdue && !bStatusInfo.isOverdue) {
          comparison = -1;
        } else if (!aStatusInfo.isOverdue && bStatusInfo.isOverdue) {
          comparison = 1;
        } else if (aStatusInfo.isOverdue && bStatusInfo.isOverdue) {
          comparison = (bStatusInfo.daysOverdue || 0) - (aStatusInfo.daysOverdue || 0);
        } else {
          const aDays = aStatusInfo.daysUntilDue ?? Infinity;
          const bDays = bStatusInfo.daysUntilDue ?? Infinity;
          comparison = aDays - bDays;
        }
        break;
      case "status":
        const statusOrder = ["active", "completed", "skipped"];
        const aStatus = calculateTaskStatusInfo(a).status;
        const bStatus = calculateTaskStatusInfo(b).status;
        comparison = statusOrder.indexOf(aStatus) - statusOrder.indexOf(bStatus);
        break;
      case "createdAt":
        comparison = new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
        break;
      default:
        comparison = 0;
    }
    
    return sortOrder === "desc" ? -comparison : comparison;
  });
}

/**
 * Calculate due date for a task based on period and due day
 */
export function calculateDueDate(period: TaskPeriod, dueDay: number | null, startDate: Date): Date | null {
  if (!dueDay) return null;
  
  const config = TASK_PERIOD_CONFIGS[period];
  const clampedDueDay = Math.min(Math.max(dueDay, 1), config.dueDayMax);
  
  const dueDate = new Date(startDate);
  
  switch (period) {
    case "weekly":
      // dueDay is day of week (1=Monday, 7=Sunday)
      const currentDay = dueDate.getDay() || 7; // Convert Sunday (0) to 7
      const daysToAdd = (clampedDueDay - currentDay + 7) % 7;
      dueDate.setDate(dueDate.getDate() + daysToAdd);
      break;
    case "monthly":
      // dueDay is day of month
      dueDate.setDate(clampedDueDay);
      break;
    case "quarterly":
      // dueDay is day of quarter (1-90)
      const quarterStart = new Date(dueDate.getFullYear(), Math.floor(dueDate.getMonth() / 3) * 3, 1);
      quarterStart.setDate(quarterStart.getDate() + clampedDueDay - 1);
      return quarterStart;
    case "yearly":
      // dueDay is day of year (1-365)
      const yearStart = new Date(dueDate.getFullYear(), 0, 1);
      yearStart.setDate(yearStart.getDate() + clampedDueDay - 1);
      return yearStart;
  }
  
  return dueDate;
}

/**
 * Format task period for display
 */
export function formatTaskPeriod(period: TaskPeriod): string {
  return TASK_PERIOD_CONFIGS[period].label;
}

/**
 * Format task due day for display
 */
export function formatTaskDueDay(period: TaskPeriod, dueDay: number | null): string | null {
  if (!dueDay) return null;
  
  const config = TASK_PERIOD_CONFIGS[period];
  
  switch (period) {
    case "weekly":
      const days = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"];
      return days[dueDay - 1] || `Day ${dueDay}`;
    case "monthly":
      return `Day ${dueDay}`;
    case "quarterly":
      return `Day ${dueDay} of quarter`;
    case "yearly":
      return `Day ${dueDay} of year`;
    default:
      return `Day ${dueDay}`;
  }
}

/**
 * Convert TaskResponse to TaskDisplayData with calculated fields
 */
export function toTaskDisplayData(task: TaskResponse): TaskDisplayData {
  return {
    ...task,
    statusInfo: calculateTaskStatusInfo(task),
  };
}
