import type {
  TaskR<PERSON>ponse,
  TaskRecordResponse,
  TaskCreateRequest,
  TaskUpdateRequest,
  TaskStatusUpdateRequest,
  TaskPeriod,
  TaskStatus,
  TaskListResponse,
  TaskHistoryResponse,
  TaskTransactionAssignment,
} from "~/api/types.gen";

// Re-export API types for convenience
export type {
  TaskResponse,
  TaskRecordResponse,
  TaskCreateRequest,
  TaskUpdateRequest,
  TaskStatusUpdateRequest,
  TaskPeriod,
  TaskStatus,
  TaskListResponse,
  TaskHistoryResponse,
  TaskTransactionAssignment,
};

// Additional types for the tasks feature

// Task status calculation
export interface TaskStatusInfo {
  status: TaskStatus;
  isOverdue: boolean;
  daysUntilDue: number | null;
  daysOverdue: number | null;
}

// Task display helpers
export interface TaskDisplayData {
  id: string;
  title: string;
  description: string | null;
  period: TaskPeriod;
  amount: string | null;
  dueDay: number | null;
  isArchived: boolean;
  sendNotifications: boolean;
  current: TaskRecordResponse;
  statusInfo: TaskStatusInfo;
  createdAt: string;
  updatedAt: string;
}

// Task filtering options
export interface TaskFilterOptions {
  status?: TaskStatus | "all" | "overdue";
  period?: TaskPeriod | "all";
  includeArchived?: boolean;
}

// Task sorting options
export type TaskSortBy = "title" | "period" | "dueDate" | "status" | "createdAt";
export type TaskSortOrder = "asc" | "desc";

export interface TaskSortOptions {
  sortBy: TaskSortBy;
  sortOrder: TaskSortOrder;
}

// Task period configuration
export interface TaskPeriodConfig {
  label: string;
  shortLabel: string;
  dueDayLabel: string;
  dueDayMax: number;
  dueDayDefault: number;
}

// Task transaction assignment
export interface TaskTransactionAssignmentData {
  taskId: string;
  transactionIds: string[];
  action: "assign" | "remove" | "replace";
}
