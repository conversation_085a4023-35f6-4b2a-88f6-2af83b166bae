import { describe, it, expect, beforeEach } from "vitest";
import { renderHook, act } from "@testing-library/react";

import type { TaskResponse } from "~/api/types.gen";

import { useTaskActionsStore } from "../store/task-actions-store";

const mockTask: TaskResponse = {
  id: "1",
  title: "Test Task",
  description: "Test description",
  period: "monthly",
  amount: "100.00",
  dueDay: 15,
  isArchived: false,
  sendNotifications: true,
  createdAt: "2024-01-01T00:00:00Z",
  updatedAt: "2024-01-01T00:00:00Z",
  current: null,
};

describe("useTaskActionsStore", () => {
  beforeEach(() => {
    // Reset store state before each test
    const { result } = renderHook(() => useTaskActionsStore());
    act(() => {
      result.current.closeTaskDialog();
      result.current.closeTransactionDialog();
    });
  });

  describe("task dialog actions", () => {
    it("should open create task dialog", () => {
      const { result } = renderHook(() => useTaskActionsStore());

      act(() => {
        result.current.openCreateTaskDialog();
      });

      expect(result.current.taskDialog.isOpen).toBe(true);
      expect(result.current.taskDialog.editingTask).toBe(null);
    });

    it("should open edit task dialog", () => {
      const { result } = renderHook(() => useTaskActionsStore());

      act(() => {
        result.current.openEditTaskDialog(mockTask);
      });

      expect(result.current.taskDialog.isOpen).toBe(true);
      expect(result.current.taskDialog.editingTask).toEqual(mockTask);
    });

    it("should close task dialog", () => {
      const { result } = renderHook(() => useTaskActionsStore());

      // First open the dialog
      act(() => {
        result.current.openCreateTaskDialog();
      });

      expect(result.current.taskDialog.isOpen).toBe(true);

      // Then close it
      act(() => {
        result.current.closeTaskDialog();
      });

      expect(result.current.taskDialog.isOpen).toBe(false);
      expect(result.current.taskDialog.editingTask).toBe(null);
    });
  });

  describe("transaction dialog actions", () => {
    it("should open transaction dialog", () => {
      const { result } = renderHook(() => useTaskActionsStore());

      act(() => {
        result.current.openTransactionDialog("task-1");
      });

      expect(result.current.transactionDialog.isOpen).toBe(true);
      expect(result.current.transactionDialog.taskId).toBe("task-1");
    });

    it("should close transaction dialog", () => {
      const { result } = renderHook(() => useTaskActionsStore());

      // First open the dialog
      act(() => {
        result.current.openTransactionDialog("task-1");
      });

      expect(result.current.transactionDialog.isOpen).toBe(true);

      // Then close it
      act(() => {
        result.current.closeTransactionDialog();
      });

      expect(result.current.transactionDialog.isOpen).toBe(false);
      expect(result.current.transactionDialog.taskId).toBe(null);
    });
  });

  describe("initial state", () => {
    it("should have correct initial state", () => {
      const { result } = renderHook(() => useTaskActionsStore());

      expect(result.current.taskDialog.isOpen).toBe(false);
      expect(result.current.taskDialog.editingTask).toBe(null);
      expect(result.current.transactionDialog.isOpen).toBe(false);
      expect(result.current.transactionDialog.taskId).toBe(null);
    });
  });
});
