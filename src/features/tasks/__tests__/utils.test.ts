import { describe, it, expect } from "vitest";

import type { TaskResponse } from "~/api/types.gen";

import {
  calculateTaskStatusInfo,
  filterTasksByStatus,
  sortTasks,
  calculateDueDate,
  formatTaskPeriod,
  formatTaskDueDay,
} from "../utils";

// Mock task data
const mockTask: TaskResponse = {
  id: "1",
  title: "Test Task",
  description: "Test description",
  period: "monthly",
  amount: "100.00",
  dueDay: 15,
  isArchived: false,
  sendNotifications: true,
  createdAt: "2024-01-01T00:00:00Z",
  updatedAt: "2024-01-01T00:00:00Z",
  current: {
    id: "current-1",
    taskId: "1",
    status: "active",
    startDate: "2024-01-01T00:00:00Z",
    endDate: "2024-01-31T23:59:59Z",
    amount: null,
    createdAt: "2024-01-01T00:00:00Z",
    updatedAt: "2024-01-01T00:00:00Z",
  },
};

describe("calculateTaskStatusInfo", () => {
  it("should calculate status info for active task", () => {
    const result = calculateTaskStatusInfo(mockTask);
    
    expect(result.status).toBe("active");
    expect(result.isOverdue).toBe(false);
    expect(typeof result.daysUntilDue).toBe("number");
  });

  it("should handle task without current period", () => {
    const taskWithoutCurrent = { ...mockTask, current: null };
    const result = calculateTaskStatusInfo(taskWithoutCurrent);
    
    expect(result.status).toBe("active");
    expect(result.isOverdue).toBe(false);
    expect(result.daysUntilDue).toBe(null);
    expect(result.daysOverdue).toBe(null);
  });

  it("should detect overdue tasks", () => {
    const overdueTask = {
      ...mockTask,
      current: {
        ...mockTask.current!,
        endDate: "2023-12-31T23:59:59Z", // Past date
      },
    };
    
    const result = calculateTaskStatusInfo(overdueTask);
    
    expect(result.isOverdue).toBe(true);
    expect(typeof result.daysOverdue).toBe("number");
    expect(result.daysOverdue).toBeGreaterThan(0);
  });
});

describe("filterTasksByStatus", () => {
  const tasks = [
    { ...mockTask, id: "1" },
    {
      ...mockTask,
      id: "2",
      current: { ...mockTask.current!, status: "completed" as const },
    },
    {
      ...mockTask,
      id: "3",
      current: { ...mockTask.current!, status: "skipped" as const },
    },
  ];

  it("should filter active tasks", () => {
    const result = filterTasksByStatus(tasks, "active");
    expect(result).toHaveLength(1);
    expect(result[0].id).toBe("1");
  });

  it("should filter completed tasks", () => {
    const result = filterTasksByStatus(tasks, "completed");
    expect(result).toHaveLength(1);
    expect(result[0].id).toBe("2");
  });

  it("should filter skipped tasks", () => {
    const result = filterTasksByStatus(tasks, "skipped");
    expect(result).toHaveLength(1);
    expect(result[0].id).toBe("3");
  });
});

describe("sortTasks", () => {
  const tasks = [
    { ...mockTask, id: "1", title: "B Task" },
    { ...mockTask, id: "2", title: "A Task" },
    { ...mockTask, id: "3", title: "C Task" },
  ];

  it("should sort by title ascending", () => {
    const result = sortTasks(tasks, { sortBy: "title", sortOrder: "asc" });
    expect(result[0].title).toBe("A Task");
    expect(result[1].title).toBe("B Task");
    expect(result[2].title).toBe("C Task");
  });

  it("should sort by title descending", () => {
    const result = sortTasks(tasks, { sortBy: "title", sortOrder: "desc" });
    expect(result[0].title).toBe("C Task");
    expect(result[1].title).toBe("B Task");
    expect(result[2].title).toBe("A Task");
  });
});

describe("calculateDueDate", () => {
  const startDate = new Date("2024-01-01");

  it("should calculate weekly due date", () => {
    const result = calculateDueDate("weekly", 1, startDate); // Monday
    expect(result).toBeInstanceOf(Date);
  });

  it("should calculate monthly due date", () => {
    const result = calculateDueDate("monthly", 15, startDate);
    expect(result).toBeInstanceOf(Date);
    expect(result?.getDate()).toBe(15);
  });

  it("should return null for null due day", () => {
    const result = calculateDueDate("monthly", null, startDate);
    expect(result).toBe(null);
  });
});

describe("formatTaskPeriod", () => {
  it("should format period labels correctly", () => {
    expect(formatTaskPeriod("weekly")).toBe("Weekly");
    expect(formatTaskPeriod("monthly")).toBe("Monthly");
    expect(formatTaskPeriod("quarterly")).toBe("Quarterly");
    expect(formatTaskPeriod("yearly")).toBe("Yearly");
  });
});

describe("formatTaskDueDay", () => {
  it("should format weekly due day", () => {
    expect(formatTaskDueDay("weekly", 1)).toBe("Monday");
    expect(formatTaskDueDay("weekly", 7)).toBe("Sunday");
  });

  it("should format monthly due day", () => {
    expect(formatTaskDueDay("monthly", 15)).toBe("Day 15");
  });

  it("should return null for null due day", () => {
    expect(formatTaskDueDay("monthly", null)).toBe(null);
  });
});
