import { describe, it, expect } from "vitest";
import { render, screen } from "@testing-library/react";

import type { TaskStatusInfo } from "../types";
import TaskStatusIndicator from "../components/task-status-indicator";

describe("TaskStatusIndicator", () => {
  const mockActiveStatus: TaskStatusInfo = {
    status: "active",
    isOverdue: false,
    daysUntilDue: 5,
    daysOverdue: null,
  };

  const mockOverdueStatus: TaskStatusInfo = {
    status: "active",
    isOverdue: true,
    daysUntilDue: null,
    daysOverdue: 3,
  };

  const mockCompletedStatus: TaskStatusInfo = {
    status: "completed",
    isOverdue: false,
    daysUntilDue: null,
    daysOverdue: null,
  };

  it("should render active status with label", () => {
    render(<TaskStatusIndicator statusInfo={mockActiveStatus} showLabel={true} />);
    
    expect(screen.getByText("Active")).toBeInTheDocument();
    expect(screen.getByText("🟡")).toBeInTheDocument();
  });

  it("should render overdue status with days overdue", () => {
    render(<TaskStatusIndicator statusInfo={mockOverdueStatus} showLabel={true} />);
    
    expect(screen.getByText("Overdue")).toBeInTheDocument();
    expect(screen.getByText("(3d)")).toBeInTheDocument();
    expect(screen.getByText("🔴")).toBeInTheDocument();
  });

  it("should render completed status", () => {
    render(<TaskStatusIndicator statusInfo={mockCompletedStatus} showLabel={true} />);
    
    expect(screen.getByText("Completed")).toBeInTheDocument();
    expect(screen.getByText("🟢")).toBeInTheDocument();
  });

  it("should render without label when showLabel is false", () => {
    render(<TaskStatusIndicator statusInfo={mockActiveStatus} showLabel={false} />);
    
    expect(screen.queryByText("Active")).not.toBeInTheDocument();
    expect(screen.getByText("🟡")).toBeInTheDocument();
  });

  it("should show days until due for urgent tasks", () => {
    const urgentStatus: TaskStatusInfo = {
      status: "active",
      isOverdue: false,
      daysUntilDue: 2,
      daysOverdue: null,
    };

    render(<TaskStatusIndicator statusInfo={urgentStatus} showLabel={true} />);
    
    expect(screen.getByText("Active")).toBeInTheDocument();
    expect(screen.getByText("(2d)")).toBeInTheDocument();
  });

  it("should not show days until due for non-urgent tasks", () => {
    const nonUrgentStatus: TaskStatusInfo = {
      status: "active",
      isOverdue: false,
      daysUntilDue: 10,
      daysOverdue: null,
    };

    render(<TaskStatusIndicator statusInfo={nonUrgentStatus} showLabel={true} />);
    
    expect(screen.getByText("Active")).toBeInTheDocument();
    expect(screen.queryByText("(10d)")).not.toBeInTheDocument();
  });
});
