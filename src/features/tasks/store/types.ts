import type { TaskResponse } from "~/api/types.gen";

export interface TaskActionsStore {
  // Task dialog state
  taskDialog: {
    isOpen: boolean;
    editingTask: TaskResponse | null;
  };

  // Transaction dialog state
  transactionDialog: {
    isOpen: boolean;
    taskId: string | null;
  };

  // Task dialog actions
  openCreateTaskDialog: () => void;
  openEditTaskDialog: (task: TaskResponse) => void;
  closeTaskDialog: () => void;

  // Transaction dialog actions
  openTransactionDialog: (taskId: string) => void;
  closeTransactionDialog: () => void;
}
