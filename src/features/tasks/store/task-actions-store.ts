import type { TaskResponse } from "~/api/types.gen";
import type { TaskActionsStore } from "./types";

import { create } from "zustand";
import { devtools } from "zustand/middleware";
import { immer } from "zustand/middleware/immer";

const useTaskActionsStore = create<TaskActionsStore>()(
  devtools(
    immer((set) => ({
      // Initial state
      taskDialog: {
        isOpen: false,
        editingTask: null,
      },
      transactionDialog: {
        isOpen: false,
        taskId: null,
      },

      // Task dialog actions
      openCreateTaskDialog() {
        set(
          (state) => {
            state.taskDialog.isOpen = true;
            state.taskDialog.editingTask = null;
          },
          undefined,
          "tasks/openCreateTaskDialog"
        );
      },

      openEditTaskDialog(task: TaskResponse) {
        set(
          (state) => {
            state.taskDialog.isOpen = true;
            state.taskDialog.editingTask = task;
          },
          undefined,
          "tasks/openEditTaskDialog"
        );
      },

      closeTaskDialog() {
        set(
          (state) => {
            state.taskDialog.isOpen = false;
            state.taskDialog.editingTask = null;
          },
          undefined,
          "tasks/closeTaskDialog"
        );
      },

      // Transaction dialog actions
      openTransactionDialog(taskId: string) {
        set(
          (state) => {
            state.transactionDialog.isOpen = true;
            state.transactionDialog.taskId = taskId;
          },
          undefined,
          "tasks/openTransactionDialog"
        );
      },

      closeTransactionDialog() {
        set(
          (state) => {
            state.transactionDialog.isOpen = false;
            state.transactionDialog.taskId = null;
          },
          undefined,
          "tasks/closeTransactionDialog"
        );
      },
    })),
    {
      name: "taskActionsStore",
    }
  )
);

export default useTaskActionsStore;
