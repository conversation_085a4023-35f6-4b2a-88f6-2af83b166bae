import type { TaskPeriod, TaskPeriodConfig } from "./types";

// Task period configurations
export const TASK_PERIOD_CONFIGS: Record<TaskPeriod, TaskPeriodConfig> = {
  weekly: {
    label: "Weekly",
    shortLabel: "Week",
    dueDayLabel: "Day of week (1=Monday, 7=Sunday)",
    dueDayMax: 7,
    dueDayDefault: 1,
  },
  monthly: {
    label: "Monthly",
    shortLabel: "Month",
    dueDayLabel: "Day of month (1-31)",
    dueDayMax: 31,
    dueDayDefault: 1,
  },
  quarterly: {
    label: "Quarterly",
    shortLabel: "Quarter",
    dueDayLabel: "Day of quarter (1-90)",
    dueDayMax: 90,
    dueDayDefault: 1,
  },
  yearly: {
    label: "Yearly",
    shortLabel: "Year",
    dueDayLabel: "Day of year (1-365)",
    dueDayMax: 365,
    dueDayDefault: 1,
  },
} as const;

// Task period options for forms
export const TASK_PERIOD_OPTIONS = [
  { value: "weekly" as const, label: "Weekly" },
  { value: "monthly" as const, label: "Monthly" },
  { value: "quarterly" as const, label: "Quarterly" },
  { value: "yearly" as const, label: "Yearly" },
] as const;

// Task status colors and labels
export const TASK_STATUS_CONFIG = {
  active: {
    label: "Active",
    color: "yellow",
    bgColor: "bg-yellow-100",
    textColor: "text-yellow-800",
    borderColor: "border-yellow-200",
    icon: "🟡",
  },
  completed: {
    label: "Completed",
    color: "green",
    bgColor: "bg-green-100",
    textColor: "text-green-800",
    borderColor: "border-green-200",
    icon: "🟢",
  },
  skipped: {
    label: "Skipped",
    color: "gray",
    bgColor: "bg-gray-100",
    textColor: "text-gray-800",
    borderColor: "border-gray-200",
    icon: "⚪",
  },
} as const;

// Overdue status configuration
export const OVERDUE_STATUS_CONFIG = {
  label: "Overdue",
  color: "red",
  bgColor: "bg-red-100",
  textColor: "text-red-800",
  borderColor: "border-red-200",
  icon: "🔴",
} as const;

// Task filter options
export const TASK_FILTER_OPTIONS = [
  { value: "all", label: "All Tasks" },
  { value: "active", label: "Active" },
  { value: "completed", label: "Completed" },
  { value: "overdue", label: "Overdue" },
  { value: "skipped", label: "Skipped" },
] as const;

// Task sort options
export const TASK_SORT_OPTIONS = [
  { value: "title", label: "Title" },
  { value: "period", label: "Period" },
  { value: "dueDate", label: "Due Date" },
  { value: "status", label: "Status" },
  { value: "createdAt", label: "Created Date" },
] as const;

// Default pagination settings
export const DEFAULT_TASKS_PAGE_SIZE = 20;

// Task validation constants
export const TASK_VALIDATION = {
  TITLE_MIN_LENGTH: 1,
  TITLE_MAX_LENGTH: 255,
  DESCRIPTION_MAX_LENGTH: 1000,
  DUE_DAY_MIN: 1,
} as const;
