import type { TaskResponse } from "~/api/types.gen";

import { Link } from "@tanstack/react-router";

import { Card, CardContent, CardHeader } from "~/components/ui/card";
import { formatCurrency } from "~/lib/utils";

import { calculateTaskStatusInfo, formatTaskDueDay } from "../utils";
import TaskPeriodBadge from "./task-period-badge";
import TaskProgressBar from "./task-progress-bar";
import TaskStatusButton from "./task-status-button";
import TaskStatusIndicator from "./task-status-indicator";

interface Props {
  task: TaskResponse;
}

export default function TaskCard({ task }: Props) {
  const statusInfo = calculateTaskStatusInfo(task);
  const current = task.current;

  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between gap-3">
          <div className="flex-1 min-w-0">
            <Link
              to="/tasks/$taskId"
              params={{ taskId: task.id }}
              className="block hover:text-primary transition-colors"
            >
              <h3 className="font-semibold text-lg leading-6 truncate">{task.title}</h3>
            </Link>
            {task.description && (
              <p className="text-sm text-muted-foreground mt-1 line-clamp-2">{task.description}</p>
            )}
          </div>
          <div className="flex items-center gap-2 shrink-0">
            <TaskPeriodBadge period={task.period} />
            <TaskStatusIndicator statusInfo={statusInfo} />
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Current period info */}
        {current && (
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span className="text-muted-foreground">Current period:</span>
              <span>
                {new Date(current.startDate).toLocaleDateString()} -{" "}
                {new Date(current.endDate).toLocaleDateString()}
              </span>
            </div>
            <TaskProgressBar task={task} />
          </div>
        )}

        {/* Amount and due day info */}
        <div className="flex justify-between items-center text-sm">
          <div className="space-y-1">
            {task.amount && (
              <div>
                <span className="text-muted-foreground">Amount: </span>
                <span className="font-medium">{formatCurrency("USD", task.amount)}</span>
              </div>
            )}
            {task.dueDay && (
              <div>
                <span className="text-muted-foreground">Due: </span>
                <span>{formatTaskDueDay(task.period, task.dueDay)}</span>
              </div>
            )}
          </div>
        </div>

        {/* Actions */}
        <div className="flex justify-end pt-2">
          <TaskStatusButton task={task} variant="compact" />
        </div>
      </CardContent>
    </Card>
  );
}
