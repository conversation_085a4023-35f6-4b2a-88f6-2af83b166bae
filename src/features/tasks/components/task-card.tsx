import type { TaskResponse } from "~/api/types.gen";

import { Link } from "@tanstack/react-router";

import { Card, CardContent, CardHeader } from "~/components/ui/card";
import { useBaseCurrency } from "~/features/auth/hooks";
import { formatCurrency } from "~/lib/formatters";

import { calculateTaskStatusInfo, formatTaskDueDay } from "../utils";
import TaskPeriodBadge from "./task-period-badge";
import TaskProgressBar from "./task-progress-bar";
import TaskStatusButton from "./task-status-button";
import TaskStatusIndicator from "./task-status-indicator";

interface Props {
  task: TaskResponse;
}

export default function TaskCard({ task }: Props) {
  const baseCurrency = useBaseCurrency();

  const statusInfo = calculateTaskStatusInfo(task);
  const current = task.current;

  return (
    <Card className="transition-shadow hover:shadow-md">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between gap-3">
          <div className="min-w-0 flex-1">
            <Link
              to="/tasks/$taskId"
              params={{ taskId: task.id }}
              className="hover:text-primary block transition-colors"
            >
              <h3 className="truncate text-lg leading-6 font-semibold">{task.title}</h3>
            </Link>
            {task.description && <p className="text-muted-foreground mt-1 line-clamp-2 text-sm">{task.description}</p>}
          </div>
          <div className="flex shrink-0 items-center gap-2">
            <TaskPeriodBadge period={task.period} />
            <TaskStatusIndicator statusInfo={statusInfo} />
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Current period info */}
        {current && (
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span className="text-muted-foreground">Current period:</span>
              <span>
                {new Date(current.startDate).toLocaleDateString()} - {new Date(current.endDate).toLocaleDateString()}
              </span>
            </div>
            <TaskProgressBar task={task} />
          </div>
        )}

        {/* Amount and due day info */}
        <div className="flex items-center justify-between text-sm">
          <div className="space-y-1">
            {task.amount && (
              <div>
                <span className="text-muted-foreground">Amount: </span>
                <span className="font-medium">{formatCurrency(baseCurrency, task.amount)}</span>
              </div>
            )}
            {task.dueDay && (
              <div>
                <span className="text-muted-foreground">Due: </span>
                <span>{formatTaskDueDay(task.period, task.dueDay)}</span>
              </div>
            )}
          </div>
        </div>

        {/* Actions */}
        <div className="flex justify-end pt-2">
          <TaskStatusButton task={task} variant="compact" />
        </div>
      </CardContent>
    </Card>
  );
}
