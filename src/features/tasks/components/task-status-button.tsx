import type { TaskResponse } from "~/api/types.gen";

import { CheckIcon, SkipForwardIcon, EditIcon, TrashIcon } from "lucide-react";

import { Button } from "~/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "~/components/ui/dropdown-menu";

import { useTaskActions } from "../hooks";
import { calculateTaskStatusInfo } from "../utils";

interface Props {
  task: TaskResponse;
  variant?: "default" | "compact";
}

export default function TaskStatusButton({ task, variant = "default" }: Props) {
  const { markCompleted, markSkipped, editTask, deleteTask, isUpdatingStatus, isDeleting } = useTaskActions();
  const statusInfo = calculateTaskStatusInfo(task);

  const canUpdateStatus = statusInfo.status === "active";

  if (variant === "compact") {
    return (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" size="sm" disabled={isUpdatingStatus || isDeleting}>
            Actions
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          {canUpdateStatus && (
            <>
              <DropdownMenuItem onClick={() => markCompleted(task)}>
                <CheckIcon className="mr-2 size-4" />
                Mark Complete
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => markSkipped(task)}>
                <SkipForwardIcon className="mr-2 size-4" />
                Skip
              </DropdownMenuItem>
              <DropdownMenuSeparator />
            </>
          )}
          <DropdownMenuItem onClick={() => editTask(task)}>
            <EditIcon className="mr-2 size-4" />
            Edit
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => deleteTask(task)} className="text-destructive">
            <TrashIcon className="mr-2 size-4" />
            Delete
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    );
  }

  return (
    <div className="flex gap-2">
      {canUpdateStatus && (
        <>
          <Button
            size="sm"
            variant="outline"
            onClick={() => markCompleted(task)}
            disabled={isUpdatingStatus || isDeleting}
          >
            <CheckIcon className="mr-2 size-4" />
            Complete
          </Button>
          <Button
            size="sm"
            variant="outline"
            onClick={() => markSkipped(task)}
            disabled={isUpdatingStatus || isDeleting}
          >
            <SkipForwardIcon className="mr-2 size-4" />
            Skip
          </Button>
        </>
      )}
      <Button
        size="sm"
        variant="outline"
        onClick={() => editTask(task)}
        disabled={isUpdatingStatus || isDeleting}
      >
        <EditIcon className="mr-2 size-4" />
        Edit
      </Button>
      <Button
        size="sm"
        variant="outline"
        onClick={() => deleteTask(task)}
        disabled={isUpdatingStatus || isDeleting}
      >
        <TrashIcon className="mr-2 size-4" />
        Delete
      </Button>
    </div>
  );
}
