import type { TaskResponse } from "~/api/types.gen";

import { CheckIcon, EditIcon, SkipForwardIcon, TrashIcon } from "lucide-react";

import { But<PERSON> } from "~/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "~/components/ui/dropdown-menu";

import { useTaskActions } from "../hooks";
import { calculateTaskStatusInfo } from "../utils";

interface Props {
  task: TaskResponse;
  variant?: "default" | "compact";
}

export default function TaskStatusButton({ task, variant = "default" }: Props) {
  const { markCompleted, markSkipped, editTask, deleteTask, isUpdatingStatus, isDeleting } = useTaskActions(task);
  const statusInfo = calculateTaskStatusInfo(task);

  const canUpdateStatus = statusInfo.status === "active";

  if (variant === "compact") {
    return (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" size="sm" disabled={isUpdatingStatus || isDeleting}>
            Actions
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          {canUpdateStatus && (
            <>
              <DropdownMenuItem onClick={() => markCompleted()}>
                <CheckIcon className="mr-2 size-4" />
                Mark Complete
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => markSkipped()}>
                <SkipForwardIcon className="mr-2 size-4" />
                Skip
              </DropdownMenuItem>
              <DropdownMenuSeparator />
            </>
          )}
          <DropdownMenuItem onClick={() => editTask()}>
            <EditIcon className="mr-2 size-4" />
            Edit
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => deleteTask()} className="text-destructive">
            <TrashIcon className="mr-2 size-4" />
            Delete
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    );
  }

  return (
    <div className="flex gap-2">
      {canUpdateStatus && (
        <>
          <Button size="sm" variant="outline" onClick={() => markCompleted()} disabled={isUpdatingStatus || isDeleting}>
            <CheckIcon className="mr-2 size-4" />
            Complete
          </Button>
          <Button size="sm" variant="outline" onClick={() => markSkipped()} disabled={isUpdatingStatus || isDeleting}>
            <SkipForwardIcon className="mr-2 size-4" />
            Skip
          </Button>
        </>
      )}
      <Button size="sm" variant="outline" onClick={() => editTask()} disabled={isUpdatingStatus || isDeleting}>
        <EditIcon className="mr-2 size-4" />
        Edit
      </Button>
      <Button size="sm" variant="outline" onClick={() => deleteTask()} disabled={isUpdatingStatus || isDeleting}>
        <TrashIcon className="mr-2 size-4" />
        Delete
      </Button>
    </div>
  );
}
