import type { TaskResponse, TaskUpdateRequest } from "~/api/types.gen";

import { useForm } from "react-hook-form";

import { zodResolver } from "@hookform/resolvers/zod";
import { LoaderIcon } from "lucide-react";

import { zTaskUpdateRequest } from "~/api/zod.gen";
import { InputSelect, InputSwitch, InputText, InputTextarea } from "~/components/inputs";
import { Button } from "~/components/ui/button";
import { DialogFooter } from "~/components/ui/dialog";
import { Form } from "~/components/ui/form";

import { TASK_PERIOD_CONFIGS, TASK_PERIOD_OPTIONS } from "../constants";
import { useUpdateTask } from "../hooks";

interface Props {
  task: TaskResponse;
  onSuccess?: () => void;
}

export default function TaskFormUpdate({ task, onSuccess }: Props) {
  const { mutate: updateTask, isPending } = useUpdateTask();

  const form = useForm<TaskUpdateRequest>({
    resolver: zod<PERSON><PERSON><PERSON><PERSON>(zTaskUpdateRequest),
    defaultValues: {
      title: task.title,
      description: task.description || "",
      period: task.period,
      amount: task.amount || "",
      dueDay: task.dueDay || undefined,
      isArchived: task.isArchived,
      sendNotifications: task.sendNotifications,
    },
  });

  const selectedPeriod = form.watch("period");
  const periodConfig = selectedPeriod ? TASK_PERIOD_CONFIGS[selectedPeriod] : null;

  const handleSubmit = form.handleSubmit((data: TaskUpdateRequest) => {
    // Convert empty strings to undefined
    const processedData = {
      ...data,
      description: data.description?.trim() || undefined,
      amount: data.amount?.trim() || undefined,
      dueDay: data.dueDay || undefined,
    };

    updateTask(
      { taskId: task.id, data: processedData },
      {
        onSuccess: () => {
          onSuccess?.();
        },
      }
    );
  });

  const periodOptions = TASK_PERIOD_OPTIONS.map((option) => ({
    value: option.value,
    label: option.label,
  }));

  return (
    <Form {...form}>
      <form onSubmit={handleSubmit} className="space-y-4">
        <InputText
          control={form.control}
          name="title"
          label="Task Title"
          placeholder="e.g., Pay monthly bills"
          required
        />

        <InputTextarea
          control={form.control}
          name="description"
          label="Description"
          hint="Optional"
          placeholder="Describe your task"
          className="resize-none"
        />

        <InputSelect
          control={form.control}
          name="period"
          label="Recurrence Period"
          placeholder="Select period"
          values={periodOptions}
          required
        />

        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <InputText
            control={form.control}
            name="amount"
            label="Amount"
            hint="Optional"
            placeholder="0.00"
            type="number"
            step="0.01"
          />

          {periodConfig && (
            <InputText
              control={form.control}
              name="dueDay"
              label={`Due Day (${periodConfig.dueDayLabel})`}
              hint="Optional"
              placeholder={periodConfig.dueDayDefault.toString()}
              type="number"
              min="1"
              max={periodConfig.dueDayMax.toString()}
            />
          )}
        </div>

        <div className="space-y-3">
          <InputSwitch
            control={form.control}
            name="sendNotifications"
            label="Send Notifications"
            description="Receive notifications when this task is due"
          />

          <InputSwitch
            control={form.control}
            name="isArchived"
            label="Archive Task"
            description="Archived tasks are hidden from the main list"
          />
        </div>

        <DialogFooter>
          <Button type="submit" disabled={isPending}>
            {isPending ? <LoaderIcon className="mr-2 size-4 animate-spin" /> : null}
            Update Task
          </Button>
        </DialogFooter>
      </form>
    </Form>
  );
}
