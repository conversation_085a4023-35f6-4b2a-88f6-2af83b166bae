import type { TaskCreateRequest } from "~/api/types.gen";

import { useForm } from "react-hook-form";

import { zodResolver } from "@hookform/resolvers/zod";
import { LoaderIcon } from "lucide-react";

import { zTaskCreateRequest } from "~/api/zod.gen";
import { InputSelect, InputSwitch, InputText, InputTextarea } from "~/components/inputs";
import { Button } from "~/components/ui/button";
import { DialogFooter } from "~/components/ui/dialog";
import { Form } from "~/components/ui/form";

import { TASK_PERIOD_OPTIONS } from "../constants";
import { useCreateTask } from "../hooks";

interface Props {
  onSuccess?: () => void;
}

export default function TaskFormCreate({ onSuccess }: Props) {
  const { mutate: createTask, isPending } = useCreateTask();

  const form = useForm<TaskCreateRequest>({
    resolver: zodResolver(zTaskCreateRequest),
    defaultValues: {
      title: "",
      description: "",
      period: "monthly",
      amount: "",
      sendNotifications: true,
    },
  });

  const handleSubmit = form.handleSubmit((data: TaskCreateRequest) => {
    // Convert empty strings to undefined
    const processedData = {
      ...data,
      description: data.description?.trim() || undefined,
      amount: data.amount?.trim() || undefined,
      dueDay: data.dueDay || undefined,
    };

    createTask(processedData, {
      onSuccess: () => {
        form.reset();
        onSuccess?.();
      },
    });
  });

  const periodOptions = TASK_PERIOD_OPTIONS.map((option) => ({
    value: option.value,
    label: option.label,
  }));

  return (
    <Form {...form}>
      <form onSubmit={handleSubmit} className="space-y-4">
        <InputText
          control={form.control}
          name="title"
          label="Task Title"
          placeholder="e.g., Pay monthly bills"
          required
        />

        <InputTextarea
          control={form.control}
          name="description"
          label="Description"
          hint="Optional"
          placeholder="Describe your task"
          className="resize-none"
        />

        <InputSelect
          control={form.control}
          name="period"
          label="Recurrence Period"
          placeholder="Select period"
          values={periodOptions}
          required
        />

        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <InputText
            control={form.control}
            name="amount"
            label="Amount"
            hint="Optional"
            placeholder="0.00"
            type="number"
            step="0.01"
          />
        </div>

        <InputSwitch
          control={form.control}
          name="sendNotifications"
          label="Send Notifications"
          description="Receive notifications when this task is due"
        />

        <DialogFooter>
          <Button type="submit" disabled={isPending}>
            {isPending ? <LoaderIcon className="mr-2 size-4 animate-spin" /> : null}
            Create Task
          </Button>
        </DialogFooter>
      </form>
    </Form>
  );
}
