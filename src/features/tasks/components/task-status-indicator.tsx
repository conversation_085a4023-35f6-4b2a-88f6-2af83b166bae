import type { TaskStatusInfo } from "../types";

import { Badge } from "~/components/ui/badge";
import { cn } from "~/lib/utils";

import { OVERDUE_STATUS_CONFIG, TASK_STATUS_CONFIG } from "../constants";

interface Props {
  statusInfo: TaskStatusInfo;
  showLabel?: boolean;
  className?: string;
}

export default function TaskStatusIndicator({ statusInfo, showLabel = true, className }: Props) {
  const config = statusInfo.isOverdue ? OVERDUE_STATUS_CONFIG : TASK_STATUS_CONFIG[statusInfo.status];

  if (!showLabel) {
    return (
      <span className={cn("text-lg", className)} title={config.label}>
        {config.icon}
      </span>
    );
  }

  return (
    <Badge
      variant="outline"
      className={cn("text-xs font-medium", config.bgColor, config.textColor, config.borderColor, className)}
    >
      <span className="mr-1">{config.icon}</span>
      {config.label}
      {statusInfo.isOverdue && statusInfo.daysOverdue && <span className="ml-1">({statusInfo.daysOverdue}d)</span>}
      {!statusInfo.isOverdue && statusInfo.daysUntilDue !== null && statusInfo.daysUntilDue <= 3 && (
        <span className="ml-1">({statusInfo.daysUntilDue}d)</span>
      )}
    </Badge>
  );
}
