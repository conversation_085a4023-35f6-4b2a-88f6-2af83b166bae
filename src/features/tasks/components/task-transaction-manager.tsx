import { PlusIcon } from "lucide-react";

import { Badge } from "~/components/ui/badge";
import { But<PERSON> } from "~/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "~/components/ui/dialog";
import { formatCurrency } from "~/lib/formatters";

import { useTaskTransactionActions } from "../hooks";
import { useTaskActionsStore } from "../store";

interface Props {
  taskId: string;
  transactions: Array<{
    id: string;
    description: string;
    amount: string;
    date: string;
    categoryName?: string;
  }>;
}

export default function TaskTransactionManager({ taskId, transactions }: Props) {
  const { openTransactionManager } = useTaskTransactionActions();

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg">Linked Transactions</CardTitle>
          <Button size="sm" onClick={() => openTransactionManager(taskId)}>
            <PlusIcon className="mr-2 size-4" />
            Manage
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {transactions.length === 0 ? (
          <p className="text-muted-foreground text-sm">No transactions linked to this task yet.</p>
        ) : (
          <div className="space-y-3">
            {transactions.map((transaction) => (
              <TransactionItem key={transaction.id} transaction={transaction} />
            ))}
            <div className="border-t pt-2">
              <div className="flex items-center justify-between text-sm font-medium">
                <span>Total:</span>
                <span>
                  {formatCurrency("USD", transactions.reduce((sum, t) => sum + parseFloat(t.amount), 0).toString())}
                </span>
              </div>
            </div>
          </div>
        )}
      </CardContent>

      <TaskTransactionDialog taskId={taskId} />
    </Card>
  );
}

interface TransactionItemProps {
  transaction: {
    id: string;
    description: string;
    amount: string;
    date: string;
    categoryName?: string;
  };
}

function TransactionItem({ transaction }: TransactionItemProps) {
  return (
    <div className="flex items-center justify-between rounded border p-2">
      <div className="min-w-0 flex-1">
        <p className="truncate text-sm font-medium">{transaction.description}</p>
        <div className="mt-1 flex items-center gap-2">
          <span className="text-muted-foreground text-xs">{new Date(transaction.date).toLocaleDateString()}</span>
          {transaction.categoryName && (
            <Badge variant="secondary" className="text-xs">
              {transaction.categoryName}
            </Badge>
          )}
        </div>
      </div>
      <span className="text-sm font-medium">{formatCurrency("USD", transaction.amount)}</span>
    </div>
  );
}

interface TaskTransactionDialogProps {
  taskId: string;
}

function TaskTransactionDialog({ taskId }: TaskTransactionDialogProps) {
  const { transactionDialog, closeTransactionDialog } = useTaskActionsStore();
  const { isOpen, taskId: dialogTaskId } = transactionDialog;

  const isThisTaskDialog = isOpen && dialogTaskId === taskId;

  return (
    <Dialog open={isThisTaskDialog} onOpenChange={closeTransactionDialog}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Manage Task Transactions</DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          <p className="text-muted-foreground text-sm">
            Select transactions to link with this task. This helps track spending related to your recurring tasks.
          </p>

          {/* TODO: Implement transaction selection interface */}
          <div className="py-8 text-center">
            <p className="text-muted-foreground">Transaction selection interface coming soon...</p>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
