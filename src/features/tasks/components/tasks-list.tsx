import type { TaskFilterOptions, TaskPeriod, TaskSortOptions, TaskStatus } from "../types";

import { useState } from "react";

import { PlusIcon } from "lucide-react";

import { Button } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select";

import { TASK_FILTER_OPTIONS, TASK_PERIOD_OPTIONS } from "../constants";
import { useTaskActions } from "../hooks";
import { useTasks } from "../hooks/use-tasks";
import TaskCard from "./task-card";

export default function TasksList() {
  const [searchQuery, setSearchQuery] = useState("");
  const [filterOptions, setFilterOptions] = useState<TaskFilterOptions>({
    status: "all",
    period: "all",
    includeArchived: false,
  });
  const [sortOptions] = useState<TaskSortOptions>({
    sortBy: "dueDate",
    sortOrder: "asc",
  });

  const { createTask } = useTaskActions();
  const { tasks, isLoading, error } = useTasks({
    ...filterOptions,
    ...sortOptions,
  });

  // Filter tasks by search query
  const filteredTasks = tasks.filter(
    (task) =>
      task.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (task.description && task.description.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  const hasResults = filteredTasks.length > 0;

  if (error) {
    return (
      <div className="py-8 text-center">
        <p className="text-destructive">Failed to load tasks. Please try again.</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with search and filters */}
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div className="max-w-md flex-1">
          <Input placeholder="Search tasks..." value={searchQuery} onChange={(e) => setSearchQuery(e.target.value)} />
        </div>

        <div className="flex gap-2">
          <Select
            value={filterOptions.status || "all"}
            onValueChange={(status: TaskStatus | "all") => setFilterOptions((prev) => ({ ...prev, status }))}
          >
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {TASK_FILTER_OPTIONS.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select
            value={filterOptions.period || "all"}
            onValueChange={(period: TaskPeriod) => setFilterOptions((prev) => ({ ...prev, period }))}
          >
            <SelectTrigger className="w-32">
              <SelectValue placeholder="All Periods" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Periods</SelectItem>
              {TASK_PERIOD_OPTIONS.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Button onClick={createTask}>
            <PlusIcon className="mr-2 size-4" />
            Add Task
          </Button>
        </div>
      </div>

      {/* Loading state */}
      {isLoading && (
        <div className="py-8 text-center">
          <p className="text-muted-foreground">Loading tasks...</p>
        </div>
      )}

      {/* Empty state */}
      {!isLoading && !hasResults && searchQuery && (
        <div className="py-8 text-center">
          <p className="text-muted-foreground">No tasks found matching "{searchQuery}".</p>
        </div>
      )}

      {!isLoading && !hasResults && !searchQuery && (
        <div className="py-8 text-center">
          <p className="text-muted-foreground mb-4">No tasks yet. Create your first task to get started!</p>
          <Button onClick={createTask}>
            <PlusIcon className="mr-2 size-4" />
            Create First Task
          </Button>
        </div>
      )}

      {/* Tasks grid */}
      {hasResults && (
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
          {filteredTasks.map((task) => (
            <TaskCard key={task.id} task={task} />
          ))}
        </div>
      )}
    </div>
  );
}
