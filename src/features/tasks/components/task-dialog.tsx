import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "~/components/ui/dialog";

import { useTaskActionsStore } from "../store";
import TaskFormCreate from "./task-form-create";
import TaskFormUpdate from "./task-form-update";

export default function TaskDialog() {
  const { taskDialog, closeTaskDialog } = useTaskActionsStore();
  const { isOpen, editingTask } = taskDialog;

  return (
    <Dialog open={isOpen} onOpenChange={closeTaskDialog}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>{editingTask ? "Edit Task" : "Create Task"}</DialogTitle>
        </DialogHeader>

        {editingTask ? (
          <TaskFormUpdate task={editingTask} onSuccess={closeTaskDialog} />
        ) : (
          <TaskFormCreate onSuccess={closeTaskDialog} />
        )}
      </DialogContent>
    </Dialog>
  );
}
