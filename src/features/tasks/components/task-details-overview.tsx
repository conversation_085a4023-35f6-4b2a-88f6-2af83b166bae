import type { TaskResponse } from "~/api/types.gen";

import { BellIcon, CalendarIcon, ClockIcon, DollarSignIcon } from "lucide-react";

import { Badge } from "~/components/ui/badge";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { formatCurrency } from "~/lib/formatters";

import { calculateTaskStatusInfo, formatTaskDueDay, formatTaskPeriod } from "../utils";
import TaskPeriodBadge from "./task-period-badge";
import TaskProgressBar from "./task-progress-bar";
import TaskStatusButton from "./task-status-button";
import TaskStatusIndicator from "./task-status-indicator";

interface Props {
  task: TaskResponse;
}

export default function TaskDetailsOverview({ task }: Props) {
  const statusInfo = calculateTaskStatusInfo(task);
  const current = task.current;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-start justify-between gap-4">
        <div className="min-w-0 flex-1">
          <h1 className="text-2xl leading-7 font-bold">{task.title}</h1>
          {task.description && <p className="text-muted-foreground mt-2">{task.description}</p>}
        </div>
        <div className="flex shrink-0 items-center gap-3">
          <TaskPeriodBadge period={task.period} />
          <TaskStatusIndicator statusInfo={statusInfo} />
        </div>
      </div>

      {/* Current Period Status */}
      {current && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Current Period</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
              <div className="flex items-center gap-2">
                <CalendarIcon className="text-muted-foreground size-4" />
                <span className="text-sm">
                  {new Date(current.startDate).toLocaleDateString()} - {new Date(current.endDate).toLocaleDateString()}
                </span>
              </div>

              {statusInfo.daysUntilDue !== null && (
                <div className="flex items-center gap-2">
                  <ClockIcon className="text-muted-foreground size-4" />
                  <span className="text-sm">
                    {statusInfo.daysUntilDue === 0 ? "Due today" : `${statusInfo.daysUntilDue} days until due`}
                  </span>
                </div>
              )}

              {statusInfo.isOverdue && statusInfo.daysOverdue && (
                <div className="flex items-center gap-2">
                  <ClockIcon className="text-destructive size-4" />
                  <span className="text-destructive text-sm">{statusInfo.daysOverdue} days overdue</span>
                </div>
              )}
            </div>

            <TaskProgressBar task={task} />

            {current.amount && (
              <div className="flex items-center gap-2">
                <DollarSignIcon className="text-muted-foreground size-4" />
                <span className="text-sm">Completed for {formatCurrency("USD", current.amount)}</span>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Task Configuration */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Configuration</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
            <div>
              <label className="text-muted-foreground text-sm font-medium">Period</label>
              <p className="text-sm">{formatTaskPeriod(task.period)}</p>
            </div>

            {task.dueDay && (
              <div>
                <label className="text-muted-foreground text-sm font-medium">Due Day</label>
                <p className="text-sm">{formatTaskDueDay(task.period, task.dueDay)}</p>
              </div>
            )}

            {task.amount && (
              <div>
                <label className="text-muted-foreground text-sm font-medium">Target Amount</label>
                <p className="text-sm">{formatCurrency("USD", task.amount)}</p>
              </div>
            )}

            <div>
              <label className="text-muted-foreground text-sm font-medium">Notifications</label>
              <div className="mt-1 flex items-center gap-2">
                <BellIcon className="text-muted-foreground size-4" />
                <span className="text-sm">{task.sendNotifications ? "Enabled" : "Disabled"}</span>
              </div>
            </div>

            <div>
              <label className="text-muted-foreground text-sm font-medium">Status</label>
              <div className="mt-1">
                {task.isArchived ? (
                  <Badge variant="secondary">Archived</Badge>
                ) : (
                  <Badge variant="outline">Active</Badge>
                )}
              </div>
            </div>

            <div>
              <label className="text-muted-foreground text-sm font-medium">Created</label>
              <p className="text-sm">{new Date(task.createdAt).toLocaleDateString()}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <TaskStatusButton task={task} variant="default" />
        </CardContent>
      </Card>
    </div>
  );
}
