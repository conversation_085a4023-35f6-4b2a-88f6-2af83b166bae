import type { TaskRecordResponse } from "~/api/types.gen";

import { Check<PERSON><PERSON>, SkipForwardIcon, XIcon } from "lucide-react";

import { Badge } from "~/components/ui/badge";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { useBaseCurrency } from "~/features/auth/hooks";
import { formatCurrency } from "~/lib/formatters";

import { TASK_STATUS_CONFIG } from "../constants";
import { useTaskHistory } from "../hooks";

interface Props {
  taskId: string;
}

export default function TaskHistoryList({ taskId }: Props) {
  const { history, isLoading, error } = useTaskHistory(taskId);

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">History</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-destructive text-sm">Failed to load task history.</p>
        </CardContent>
      </Card>
    );
  }

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">History</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground text-sm">Loading history...</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg">History</CardTitle>
      </CardHeader>
      <CardContent>
        {history.length === 0 ? (
          <p className="text-muted-foreground text-sm">No history records yet.</p>
        ) : (
          <div className="space-y-4">
            {history.map((record) => (
              <TaskHistoryItem key={record.id} record={record} />
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}

interface TaskHistoryItemProps {
  record: TaskRecordResponse;
}

function TaskHistoryItem({ record }: TaskHistoryItemProps) {
  const baseCurrency = useBaseCurrency();

  if (!record) {
    return null;
  }

  const config = TASK_STATUS_CONFIG[record.status];

  const getStatusIcon = () => {
    switch (record.status) {
      case "completed":
        return <CheckIcon className="size-4 text-green-600" />;
      case "skipped":
        return <SkipForwardIcon className="size-4 text-yellow-600" />;
      default:
        return <XIcon className="size-4 text-gray-400" />;
    }
  };

  return (
    <div className="flex items-start gap-3 rounded-lg border p-3">
      <div className="mt-0.5 shrink-0">{getStatusIcon()}</div>

      <div className="min-w-0 flex-1">
        <div className="mb-1 flex items-center gap-2">
          <Badge variant="outline" className="text-xs">
            {config.label}
          </Badge>
          <span className="text-muted-foreground text-xs">
            {new Date(record.startDate).toLocaleDateString()} - {new Date(record.endDate).toLocaleDateString()}
          </span>
        </div>

        {record.amount && (
          <p className="text-muted-foreground text-sm">Amount: {formatCurrency(baseCurrency, record.amount)}</p>
        )}

        <p className="text-muted-foreground mt-1 text-xs">
          {record.status === "completed" ? "Completed" : "Skipped"} on {new Date(record.updatedAt).toLocaleDateString()}
        </p>
      </div>
    </div>
  );
}
