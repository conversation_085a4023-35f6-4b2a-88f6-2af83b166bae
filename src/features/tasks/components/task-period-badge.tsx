import type { TaskPeriod } from "~/api/types.gen";

import { Badge } from "~/components/ui/badge";
import { cn } from "~/lib/utils";

import { TASK_PERIOD_CONFIGS } from "../constants";

interface Props {
  period: TaskPeriod;
  variant?: "default" | "secondary" | "outline";
  className?: string;
}

export default function TaskPeriodBadge({ period, variant = "secondary", className }: Props) {
  const config = TASK_PERIOD_CONFIGS[period];

  return (
    <Badge variant={variant} className={cn("text-xs", className)}>
      {config.shortLabel}
    </Badge>
  );
}
