import type { TaskResponse } from "~/api/types.gen";

import { Progress } from "~/components/ui/progress";
import { cn } from "~/lib/utils";

import { calculateTaskStatusInfo } from "../utils";

interface Props {
  task: TaskResponse;
  className?: string;
}

export default function TaskProgressBar({ task, className }: Props) {
  const statusInfo = calculateTaskStatusInfo(task);
  const current = task.current;

  if (!current) {
    return null;
  }

  // Calculate progress based on time elapsed in the current period
  const startDate = new Date(current.startDate);
  const endDate = new Date(current.endDate);
  const now = new Date();

  const totalDuration = endDate.getTime() - startDate.getTime();
  const elapsedDuration = Math.min(now.getTime() - startDate.getTime(), totalDuration);
  const progress = Math.max(0, Math.min(100, (elapsedDuration / totalDuration) * 100));

  // Determine color based on status
  let progressColor = "bg-blue-500";
  if (statusInfo.status === "completed") {
    progressColor = "bg-green-500";
  } else if (statusInfo.status === "skipped") {
    progressColor = "bg-gray-400";
  } else if (statusInfo.isOverdue) {
    progressColor = "bg-red-500";
  }

  return (
    <div className={cn("space-y-1", className)}>
      <Progress 
        value={progress} 
        className="h-2"
        // Note: Progress component styling would need to be customized to support different colors
      />
      <div className="flex justify-between text-xs text-muted-foreground">
        <span>{startDate.toLocaleDateString()}</span>
        <span>{endDate.toLocaleDateString()}</span>
      </div>
    </div>
  );
}
