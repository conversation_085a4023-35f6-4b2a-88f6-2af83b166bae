// Goal-related constants and configuration

export const GOAL_COLORS = [
  "#ef4444", // red-500
  "#f97316", // orange-500
  "#eab308", // yellow-500
  "#22c55e", // green-500
  "#06b6d4", // cyan-500
  "#3b82f6", // blue-500
  "#8b5cf6", // violet-500
  "#ec4899", // pink-500
  "#64748b", // slate-500
  "#6b7280", // gray-500
] as const;

export const DEFAULT_GOAL_COLOR = "#3b82f6"; // blue-500

// Goal progress thresholds
export const GOAL_PROGRESS_THRESHOLDS = {
  WARNING: 75, // Show warning at 75%
  COMPLETE: 100, // Goal is complete at 100%
} as const;

// Goal icons (using Lucide icon names)
export const GOAL_ICON_OPTIONS = [
  { value: "target", label: "Target" },
  { value: "piggy-bank", label: "Piggy Bank" },
  { value: "home", label: "Home" },
  { value: "car", label: "Car" },
  { value: "plane", label: "Travel" },
  { value: "graduation-cap", label: "Education" },
  { value: "heart", label: "Health" },
  { value: "gift", label: "Gift" },
  { value: "shopping-bag", label: "Shopping" },
  { value: "briefcase", label: "Business" },
  { value: "trophy", label: "Achievement" },
  { value: "star", label: "Star" },
] as const;

export const DEFAULT_GOAL_ICON = "target";