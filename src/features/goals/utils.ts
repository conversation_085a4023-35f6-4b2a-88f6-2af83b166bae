import type { Goal } from "~/api/types.gen";
import type { GoalProgressStatus } from "./types";

import { GOAL_PROGRESS_THRESHOLDS } from "./constants";

/**
 * Calculate goal progress status based on current and target amounts
 */
export function calculateGoalProgress(currentAmount: string, targetAmount: string | null): GoalProgressStatus {
  const current = parseFloat(currentAmount) || 0;
  const target = parseFloat(targetAmount || "0") || 0;

  if (target === 0) {
    return {
      percentage: 0,
      status: "under",
      isCompleted: false,
    };
  }

  const percentage = Math.round((current / target) * 100);

  let status: GoalProgressStatus["status"] = "under";
  if (percentage >= GOAL_PROGRESS_THRESHOLDS.COMPLETE) {
    status = "over";
  } else if (percentage >= GOAL_PROGRESS_THRESHOLDS.WARNING) {
    status = "warning";
  }

  return {
    percentage,
    status,
    isCompleted: percentage >= GOAL_PROGRESS_THRESHOLDS.COMPLETE,
  };
}

/**
 * Format goal target date for display
 */
export function formatGoalTargetDate(targetDate: string | null): string | null {
  if (!targetDate) return null;

  try {
    const date = new Date(targetDate);
    return date.toLocaleDateString(undefined, {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  } catch {
    return null;
  }
}

/**
 * Check if goal target date is approaching (within 30 days)
 */
export function isGoalTargetDateApproaching(targetDate: string | null): boolean {
  if (!targetDate) return false;

  try {
    const target = new Date(targetDate);
    const now = new Date();
    const diffTime = target.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    return diffDays <= 30 && diffDays >= 0;
  } catch {
    return false;
  }
}

/**
 * Check if goal target date has passed
 */
export function isGoalTargetDatePassed(targetDate: string | null): boolean {
  if (!targetDate) return false;

  try {
    const target = new Date(targetDate);
    const now = new Date();
    return target < now;
  } catch {
    return false;
  }
}

/**
 * Sort goals by priority (incomplete goals with approaching deadlines first)
 */
export function sortGoalsByPriority(goals: Goal[]): Goal[] {
  return [...goals].sort((a, b) => {
    const aProgress = calculateGoalProgress(a.currentAmount, a.targetAmount);
    const bProgress = calculateGoalProgress(b.currentAmount, b.targetAmount);

    // Completed goals go to the end
    if (aProgress.isCompleted && !bProgress.isCompleted) return 1;
    if (!aProgress.isCompleted && bProgress.isCompleted) return -1;

    // For incomplete goals, prioritize by approaching deadline
    if (!aProgress.isCompleted && !bProgress.isCompleted) {
      const aApproaching = isGoalTargetDateApproaching(a.targetDate);
      const bApproaching = isGoalTargetDateApproaching(b.targetDate);

      if (aApproaching && !bApproaching) return -1;
      if (!aApproaching && bApproaching) return 1;

      // If both or neither are approaching, sort by target date
      if (a.targetDate && b.targetDate) {
        return new Date(a.targetDate).getTime() - new Date(b.targetDate).getTime();
      }
      if (a.targetDate && !b.targetDate) return -1;
      if (!a.targetDate && b.targetDate) return 1;
    }

    // Finally, sort by creation date (newest first)
    return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
  });
}

/**
 * Filter goals by completion status
 */
export function filterGoalsByCompletion(goals: Goal[], showCompleted: boolean): Goal[] {
  return goals.filter((goal) => {
    const progress = calculateGoalProgress(goal.currentAmount, goal.targetAmount);
    return showCompleted ? progress.isCompleted : !progress.isCompleted;
  });
}