import type {
  Goal,
  GoalCreateRequest,
  GoalUpdateRequest,
} from "~/api/types.gen";

// Re-export API types for convenience
export type {
  Goal,
  GoalCreateRequest,
  GoalUpdateRequest,
};

// Additional types for the goals feature

// Goal progress calculation
export interface GoalProgressStatus {
  percentage: number;
  status: "under" | "warning" | "over"; // under 75%, 75-100%, over 100%
  isCompleted: boolean;
}

// Goal display helpers
export interface GoalDisplayData {
  id: string;
  name: string;
  description: string | null;
  targetAmount: string | null;
  currentAmount: string;
  progress: GoalProgressStatus;
  targetDate: string | null;
  color: string | null;
  iconUrl: string | null;
  accountsCount: number;
  currency: string;
  createdAt: string;
  updatedAt: string;
}