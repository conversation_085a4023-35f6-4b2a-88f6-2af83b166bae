import type { Goal } from "~/api/types.gen";
import type { GoalActionsStore } from "./types";

import { create } from "zustand";
import { devtools } from "zustand/middleware";
import { immer } from "zustand/middleware/immer";

const useGoalActionsStore = create<GoalActionsStore>()(
  devtools(
    immer((set) => ({
      // Initial state
      goalDialog: {
        isOpen: false,
        editingGoal: null,
      },

      // Goal dialog actions
      openCreateGoalDialog() {
        set(
          (state) => {
            state.goalDialog.isOpen = true;
            state.goalDialog.editingGoal = null;
          },
          undefined,
          "goals/openCreateGoalDialog"
        );
      },

      openEditGoalDialog(goal: Goal) {
        set(
          (state) => {
            state.goalDialog.isOpen = true;
            state.goalDialog.editingGoal = goal;
          },
          undefined,
          "goals/openEditGoalDialog"
        );
      },

      closeGoalDialog() {
        set(
          (state) => {
            state.goalDialog.isOpen = false;
            state.goalDialog.editingGoal = null;
          },
          undefined,
          "goals/closeGoalDialog"
        );
      },
    })),
    {
      name: "goalActionsStore",
    }
  )
);

export default useGoalActionsStore;