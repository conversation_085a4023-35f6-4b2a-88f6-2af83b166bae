import { 
  Target, 
  PiggyBank, 
  Home, 
  Car, 
  Plane, 
  GraduationCap, 
  Heart, 
  Gift, 
  ShoppingBag, 
  Briefcase, 
  Trophy, 
  Star,
  type LucideIcon 
} from "lucide-react";

import { cn } from "~/lib/utils";

interface Props {
  iconName?: string | null;
  color?: string | null;
  className?: string;
  size?: number;
}

const iconMap: Record<string, LucideIcon> = {
  target: Target,
  "piggy-bank": PiggyBank,
  home: Home,
  car: Car,
  plane: Plane,
  "graduation-cap": GraduationCap,
  heart: Heart,
  gift: Gift,
  "shopping-bag": ShoppingBag,
  briefcase: Briefcase,
  trophy: Trophy,
  star: Star,
};

export default function GoalIcon({ iconName, color, className, size = 20 }: Props) {
  // Handle both icon names and URLs - for now just use Target as default
  const IconComponent = iconName && iconMap[iconName] ? iconMap[iconName] : Target;

  return (
    <div
      className={cn(
        "flex items-center justify-center rounded-full p-2",
        className
      )}
      style={{ backgroundColor: color ? `${color}20` : undefined }}
    >
      <IconComponent 
        size={size} 
        style={{ color: color || undefined }}
        className={!color ? "text-muted-foreground" : undefined}
      />
    </div>
  );
}