import type { Goal } from "~/api/types.gen";

import { <PERSON> } from "@tanstack/react-router";
import { Calendar, Edit, ExternalLink, MoreHorizontal, Trash2, Users } from "lucide-react";

import { Button } from "~/components/ui/button";
import { Card, CardContent } from "~/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLinkItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "~/components/ui/dropdown-menu";
import { formatCurrency } from "~/lib/formatters";

import { useDeleteGoal, useGoalActions } from "../hooks";
import {
  calculateGoalProgress,
  formatGoalTargetDate,
  isGoalTargetDateApproaching,
  isGoalTargetDatePassed,
} from "../utils";
import GoalIcon from "./goal-icon";
import GoalProgressBar from "./goal-progress-bar";

interface Props {
  goal: Goal;
}

export default function GoalRow({ goal }: Props) {
  const { editGoal } = useGoalActions();
  const { deleteGoal, isLoading: isDeleting } = useDeleteGoal();

  const progress = calculateGoalProgress(goal.currentAmount, goal.targetAmount);
  const formattedTargetDate = formatGoalTargetDate(goal.targetDate);
  const isDateApproaching = isGoalTargetDateApproaching(goal.targetDate);
  const isDatePassed = isGoalTargetDatePassed(goal.targetDate);

  const handleEdit = () => {
    editGoal(goal);
  };

  const handleDelete = () => {
    deleteGoal(goal.id, goal.name);
  };

  return (
    <Card>
      <CardContent>
        <div className="flex items-start justify-between">
          <div className="flex flex-1 items-start space-x-4">
            <GoalIcon iconName={goal.iconUrl} color={goal.color} size={24} />

            <div className="flex-1 space-y-3">
              <div>
                <div className="flex items-center gap-2">
                  <h3 className="text-lg font-semibold">{goal.name}</h3>
                  <Link to="/goals/$goalId" params={{ goalId: goal.id }}>
                    <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                      <ExternalLink className="h-3 w-3" />
                    </Button>
                  </Link>
                </div>
                {goal.description && <p className="text-muted-foreground mt-1 text-sm">{goal.description}</p>}
              </div>

              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-muted-foreground">Current Amount</span>
                    <span className="font-medium">{formatCurrency(goal.currency, goal.currentAmount)}</span>
                  </div>
                  {goal.targetAmount && (
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-muted-foreground">Target Amount</span>
                      <span className="font-medium">{formatCurrency(goal.currency, goal.targetAmount)}</span>
                    </div>
                  )}
                </div>

                <div className="space-y-2">
                  {formattedTargetDate && (
                    <div className="flex items-center space-x-2 text-sm">
                      <Calendar className="text-muted-foreground h-4 w-4" />
                      <span className="text-muted-foreground">Target Date:</span>
                      <span
                        className={`font-medium ${
                          isDatePassed ? "text-red-600" : isDateApproaching ? "text-yellow-600" : ""
                        }`}
                      >
                        {formattedTargetDate}
                        {isDatePassed && " (Overdue)"}
                        {isDateApproaching && !isDatePassed && " (Soon)"}
                      </span>
                    </div>
                  )}

                  <div className="flex items-center space-x-2 text-sm">
                    <Users className="text-muted-foreground h-4 w-4" />
                    <span className="text-muted-foreground">
                      {goal.accounts.length} account{goal.accounts.length !== 1 ? "s" : ""} connected
                    </span>
                  </div>
                </div>
              </div>

              {goal.targetAmount && <GoalProgressBar progress={progress} />}
            </div>
          </div>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLinkItem to="/goals/$goalId" params={{ goalId: goal.id }}>
                <ExternalLink className="mr-2 h-4 w-4" />
                View Details
              </DropdownMenuLinkItem>
              <DropdownMenuItem onClick={handleEdit}>
                <Edit className="mr-2 h-4 w-4" />
                Edit Goal
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onClick={handleDelete}
                disabled={isDeleting}
                className="text-red-600 focus:text-red-600"
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Delete Goal
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardContent>
    </Card>
  );
}
