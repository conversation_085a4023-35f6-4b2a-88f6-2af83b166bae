import type { Goal } from "../types";

import { useState } from "react";

import { PlusIcon } from "lucide-react";

import { LoadingIndicator } from "~/components/elements";
import { Button } from "~/components/ui/button";
import { Label } from "~/components/ui/label";
import { Switch } from "~/components/ui/switch";

import { useGoalActions } from "../hooks";
import { filterGoalsByCompletion } from "../utils";
import GoalRow from "./goal-row";

interface Props {
  goals: Goal[];
  isLoading?: boolean;
}

export default function GoalsList(props: Props) {
  const { goals, isLoading = false } = props;

  const { createGoal } = useGoalActions();

  const [showCompleted, setShowCompleted] = useState(false);

  const activeGoals = filterGoalsByCompletion(goals, false);
  const completedGoals = filterGoalsByCompletion(goals, true);
  const displayedGoals = showCompleted ? goals : activeGoals;

  if (isLoading) {
    return <LoadingIndicator />;
  }

  return (
    <div className="space-y-6">
      {/* Header controls */}
      <div className="flex flex-col items-start justify-between gap-4 sm:flex-row sm:items-center">
        <div className="flex items-center space-x-2">
          {completedGoals.length > 0 && (
            <>
              <Switch id="show-completed" checked={showCompleted} onCheckedChange={setShowCompleted} />
              <Label htmlFor="show-completed">Show completed goals</Label>
              {showCompleted && (
                <span className="text-muted-foreground text-sm">({completedGoals.length} completed)</span>
              )}
            </>
          )}
        </div>

        <Button onClick={createGoal}>
          <PlusIcon className="mr-2" />
          Add Goal
        </Button>
      </div>

      {/* Goals grid */}
      {displayedGoals.length === 0 ? (
        <div className="py-12 text-center">
          <p className="text-muted-foreground mb-4">
            {showCompleted
              ? "No goals found"
              : activeGoals.length === 0 && completedGoals.length > 0
                ? "No active goals. Toggle to show completed goals."
                : "No goals found"}
          </p>
          {!showCompleted && activeGoals.length === 0 && (
            <Button onClick={createGoal}>
              <PlusIcon className="mr-2 h-4 w-4" />
              Create your first goal
            </Button>
          )}
        </div>
      ) : (
        <div className="flex flex-col gap-6">
          {displayedGoals.map((goal) => (
            <GoalRow key={goal.id} goal={goal} />
          ))}
        </div>
      )}

      {/* Summary info */}
      {!showCompleted && activeGoals.length > 0 && completedGoals.length > 0 && (
        <div className="text-muted-foreground text-center text-sm">
          Showing {activeGoals.length} active goal{activeGoals.length !== 1 ? "s" : ""}.{" "}
          <button onClick={() => setShowCompleted(true)} className="text-primary hover:underline">
            Show {completedGoals.length} completed goal{completedGoals.length !== 1 ? "s" : ""}
          </button>
        </div>
      )}
    </div>
  );
}