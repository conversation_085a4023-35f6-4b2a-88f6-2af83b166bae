import type { GoalCreateRequest } from "~/api/types.gen";

import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { LoaderIcon } from "lucide-react";

import { zGoalCreateRequest } from "~/api/zod.gen";
import { Button } from "~/components/ui/button";
import { DialogFooter } from "~/components/ui/dialog";
import { Form } from "~/components/ui/form";
import { InputText, InputTextarea, InputColor, InputMultiSelect } from "~/components/inputs";
import { useAccounts } from "~/features/accounts/hooks";

import { GOAL_COLORS, DEFAULT_GOAL_COLOR } from "../constants";
import { useCreateGoal } from "../hooks";
import { useGoalActionsStore } from "../store";

export default function GoalFormCreate() {
  const { accounts } = useAccounts();
  const { createGoal, isLoading } = useCreateGoal();
  const { closeGoalDialog } = useGoalActionsStore();

  const form = useForm<GoalCreateRequest>({
    resolver: zodResolver(zGoalCreateRequest),
    defaultValues: {
      name: "",
      description: "",
      accounts: [],
      targetAmount: "",
      color: DEFAULT_GOAL_COLOR,
      targetDate: undefined,
    },
  });

  const handleSubmit = form.handleSubmit((data: GoalCreateRequest) => {
    // Convert empty strings to undefined and handle iconUrl specially
    const processedData = {
      ...data,
      description: data.description?.trim() || undefined,
      color: data.color?.trim() || undefined,
      targetAmount: data.targetAmount?.trim() || undefined,
      // For now, don't send iconUrl since we're using icon names, not URLs
      iconUrl: undefined,
    };

    createGoal(processedData);
  });

  const accountOptions = accounts.map((account) => ({
    value: account.id,
    label: account.name,
  }));


  return (
    <Form {...form}>
      <form onSubmit={handleSubmit} className="space-y-4">
        <InputText
          control={form.control}
          name="name"
          label="Goal Name"
          placeholder="e.g., Emergency Fund"
        />

        <InputTextarea
          control={form.control}
          name="description"
          label="Description"
          hint="Optional"
          placeholder="Describe your goal"
          className="resize-none"
        />

        <InputMultiSelect
          control={form.control}
          name="accounts"
          label="Connected Accounts"
          placeholder="Select accounts to track"
          options={accountOptions}
        />

        <InputText
          control={form.control}
          name="targetAmount"
          label="Target Amount"
          hint="Optional"
          placeholder="0.00"
          type="number"
          step="0.01"
        />

        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <InputColor
            control={form.control}
            name="color"
            label="Color"
            hint="Optional"
            colors={GOAL_COLORS}
          />

          <div className="space-y-2">
            <label className="text-sm font-medium">Target Date</label>
            <p className="text-xs text-muted-foreground">Optional</p>
            <input
              type="date"
              {...form.register("targetDate")}
              className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
            />
          </div>
        </div>

        <DialogFooter>
          <Button type="submit" disabled={form.formState.isSubmitting}>
            {form.formState.isSubmitting ? <LoaderIcon className="animate-spin" /> : "Create Goal"}
          </Button>
          <Button type="button" variant="outline" onClick={closeGoalDialog} disabled={form.formState.isSubmitting}>
            Cancel
          </Button>
        </DialogFooter>
      </form>
    </Form>
  );
}