import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON>eader, DialogTitle } from "~/components/ui/dialog";

import { useGoalActionsStore } from "../store";
import GoalFormCreate from "./goal-form-create";
import GoalFormUpdate from "./goal-form-update";

export default function GoalDialog() {
  const { goalDialog, closeGoalDialog } = useGoalActionsStore();

  const isEditing = !!goalDialog.editingGoal;

  return (
    <Dialog open={goalDialog.isOpen} onOpenChange={closeGoalDialog}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {isEditing ? "Edit Goal" : "Create New Goal"}
          </DialogTitle>
        </DialogHeader>

        {isEditing && goalDialog.editingGoal ? (
          <GoalFormUpdate goal={goalDialog.editingGoal} />
        ) : (
          <GoalFormCreate />
        )}
      </DialogContent>
    </Dialog>
  );
}