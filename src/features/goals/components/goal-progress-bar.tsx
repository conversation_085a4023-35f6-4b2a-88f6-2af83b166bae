import type { GoalProgressStatus } from "../types";

import { Progress } from "~/components/ui/progress";
import { cn } from "~/lib/utils";

interface Props {
  progress: GoalProgressStatus;
  className?: string;
}

export default function GoalProgressBar({ progress, className }: Props) {

  return (
    <div className={cn("space-y-2", className)}>
      <div className="flex items-center justify-between text-sm">
        <span className="text-muted-foreground">Progress</span>
        <span className={cn("font-medium", progress.isCompleted && "text-green-600")}>
          {progress.percentage}%
        </span>
      </div>
      <Progress 
        value={Math.min(progress.percentage, 100)} 
        className="h-2"
      />
      {progress.isCompleted && (
        <div className="text-green-600 text-xs font-medium">
          🎉 Goal completed!
        </div>
      )}
    </div>
  );
}