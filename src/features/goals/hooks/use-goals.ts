import { useMemo } from "react";

import { useQuery } from "@tanstack/react-query";

import { listGoalsOptions } from "~/api/@tanstack/react-query.gen";

import { filterGoalsByCompletion, sortGoalsByPriority } from "../utils";

interface UseGoalsOptions {
  includeCompleted?: boolean;
  sortByPriority?: boolean;
}

export function useGoals(options: UseGoalsOptions = {}) {
  const { includeCompleted = true, sortByPriority = true } = options;

  const { data: goalsData, isLoading, error, refetch } = useQuery(listGoalsOptions());

  const goals = useMemo(() => {
    let processedGoals = goalsData ?? [];

    // Filter by completion status if needed
    if (!includeCompleted) {
      processedGoals = filterGoalsByCompletion(processedGoals, false);
    }

    // Sort by priority if requested
    if (sortByPriority) {
      processedGoals = sortGoalsByPriority(processedGoals);
    }

    return processedGoals;
  }, [goalsData, includeCompleted, sortByPriority]);

  const completedGoals = useMemo(() => filterGoalsByCompletion(goalsData ?? [], true), [goalsData]);
  const activeGoals = useMemo(() => filterGoalsByCompletion(goalsData ?? [], false), [goalsData]);

  return { 
    goals, 
    completedGoals, 
    activeGoals, 
    isLoading, 
    error, 
    refetch 
  };
}