import { renderHook } from "@testing-library/react";
import { describe, it, expect, vi } from "vitest";

import { useGoalActions } from "../use-goal-actions";

// Mock the store
vi.mock("../../store", () => ({
  useGoalActionsStore: vi.fn(() => ({
    openCreateGoalDialog: vi.fn(),
    openEditGoalDialog: vi.fn(),
  })),
}));

describe("useGoalActions", () => {
  it("should provide goal action functions", () => {
    const { result } = renderHook(() => useGoalActions());

    expect(result.current.createGoal).toBeDefined();
    expect(result.current.editGoal).toBeDefined();
    expect(typeof result.current.createGoal).toBe("function");
    expect(typeof result.current.editGoal).toBe("function");
  });
});