import type { Goal } from "~/api/types.gen";

import { useGoalActionsStore } from "../store";

/**
 * Hook that provides goal action functions for UI interactions.
 * This is a convenience hook that wraps the store actions.
 */
export function useGoalActions() {
  const { openCreateGoalDialog, openEditGoalDialog } = useGoalActionsStore();

  const createGoal = () => {
    openCreateGoalDialog();
  };

  const editGoal = (goal: Goal) => {
    openEditGoalDialog(goal);
  };

  return { createGoal, editGoal };
}
