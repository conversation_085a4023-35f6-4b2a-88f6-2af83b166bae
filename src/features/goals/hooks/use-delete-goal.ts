import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { useRouter } from "@tanstack/react-router";

import { deleteGoalMutation, listGoalsQueryKey } from "~/api/@tanstack/react-query.gen";
import { useConfirm } from "~/features/ui/confirmations/hooks";

export function useDeleteGoal() {
  const queryClient = useQueryClient();
  const router = useRouter();
  const confirm = useConfirm();

  const mutation = useMutation({
    ...deleteGoalMutation(),
    onSuccess: () => {
      // Invalidate goals list to refetch
      void queryClient.invalidateQueries({
        queryKey: listGoalsQueryKey(),
      });

      toast.success("Goal deleted successfully");

      // Navigate back to goals list if we're on a goal detail page
      const currentPath = window.location.pathname;
      if (currentPath.includes('/goals/') && currentPath !== '/goals') {
        void router.navigate({ to: '/goals' });
      }
    },
    onError: (error) => {
      toast.error("Failed to delete goal", {
        description: error.message || "An unexpected error occurred.",
      });
    },
  });

  const deleteGoal = async (goalId: string, goalName?: string) => {
    const confirmed = await confirm({
      title: "Delete Goal",
      description: `Are you sure you want to delete ${goalName ? `"${goalName}"` : "this goal"}? This action cannot be undone.`,
      confirmText: "Delete",
      variant: "destructive",
    });

    if (confirmed) {
      mutation.mutate({
        path: { id: goalId },
      });
    }
  };

  return {
    deleteGoal,
    isLoading: mutation.isPending,
    error: mutation.error,
  };
}