import type { GoalUpdateRequest } from "~/api/types.gen";

import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

import { getGoalQueryKey, listGoalsQueryKey, updateGoalMutation } from "~/api/@tanstack/react-query.gen";

import { useGoalActionsStore } from "../store";

export function useUpdateGoal() {
  const queryClient = useQueryClient();

  const { closeGoalDialog } = useGoalActionsStore();

  const mutation = useMutation({
    ...updateGoalMutation(),
    onSuccess: (data) => {
      // Invalidate goals list and specific goal to refetch
      void queryClient.invalidateQueries({
        queryKey: listGoalsQueryKey(),
      });
      void queryClient.invalidateQueries({
        queryKey: getGoalQueryKey({ path: { id: data.id } }),
      });

      toast.success("Goal updated successfully!", {
        description: `"${data.name}" has been updated.`,
      });

      closeGoalDialog();
    },
    onError: (error) => {
      toast.error("Failed to update goal", {
        description: error.message || "An unexpected error occurred.",
      });
    },
  });

  const updateGoal = (goalId: string, data: GoalUpdateRequest) => {
    mutation.mutate({
      path: { id: goalId },
      body: data,
    });
  };

  return {
    updateGoal,
    isLoading: mutation.isPending,
    error: mutation.error,
  };
}