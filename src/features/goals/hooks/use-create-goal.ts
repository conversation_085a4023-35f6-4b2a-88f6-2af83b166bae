import type { GoalCreateRequest } from "~/api/types.gen";

import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

import { createGoalMutation, listGoalsQueryKey } from "~/api/@tanstack/react-query.gen";

import { useGoalActionsStore } from "../store";

export function useCreateGoal() {
  const queryClient = useQueryClient();

  const { closeGoalDialog } = useGoalActionsStore();

  const mutation = useMutation({
    ...createGoalMutation(),
    onSuccess: (data) => {
      // Invalidate goals list to refetch
      void queryClient.invalidateQueries({
        queryKey: listGoalsQueryKey(),
      });

      toast.success("Goal created successfully!", {
        description: `"${data.name}" has been added to your goals.`,
      });

      closeGoalDialog();
    },
    onError: (error) => {
      toast.error("Failed to create goal", {
        description: error.message || "An unexpected error occurred.",
      });
    },
  });

  const createGoal = (data: GoalCreateRequest) => {
    mutation.mutate({
      body: data,
    });
  };

  return {
    createGoal,
    isLoading: mutation.isPending,
    error: mutation.error,
  };
}