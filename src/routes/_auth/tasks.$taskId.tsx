import { ErrorMessage, PageHeader } from "~/components/blocks";
import { LoadingIndicator } from "~/components/elements";
import { TaskDetailsOverview, TaskDialog, TaskHistoryList, TaskTransactionManager } from "~/features/tasks/components";
import { useTask } from "~/features/tasks/hooks";

export const Route = createFileRoute({
  component: RouteComponent,
});

const transactions: Array<{
  id: string;
  description: string;
  amount: string;
  date: string;
  categoryName?: string;
}> = [];

function RouteComponent() {
  const { taskId } = Route.useParams();
  const { task, isLoading, error } = useTask(taskId);

  if (isLoading) {
    return (
      <>
        <PageHeader title="Task Details" backLink={{ to: "/tasks" }} />
        <LoadingIndicator />
      </>
    );
  }

  if (error) {
    return (
      <>
        <PageHeader title="Task Details" backLink={{ to: "/tasks" }} />
        <ErrorMessage title="Failed to load task" error={error} />
      </>
    );
  }

  if (!task) {
    return (
      <>
        <PageHeader title="Task Details" backLink={{ to: "/tasks" }} />
        <ErrorMessage title="Task not found" error={new Error("The requested task could not be found.")} />
      </>
    );
  }

  return (
    <div className="space-y-6">
      <PageHeader title={task.title} backLink={{ to: "/tasks" }} />

      {/* Main content */}
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
        {/* Left column - Task overview */}
        <div className="lg:col-span-2">
          <TaskDetailsOverview task={task} />
        </div>

        {/* Right column - History and transactions */}
        <div className="space-y-6">
          <TaskHistoryList taskId={task.id} />
          <TaskTransactionManager taskId={task.id} transactions={transactions} />
        </div>
      </div>

      <TaskDialog />
    </div>
  );
}
