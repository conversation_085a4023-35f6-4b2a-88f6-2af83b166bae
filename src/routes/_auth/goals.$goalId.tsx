import { ErrorMessage, PageHeader } from "~/components/blocks";
import { LoadingIndicator } from "~/components/elements";
import { GoalDetailsOverview, GoalDialog } from "~/features/goals/components";
import { useGoal } from "~/features/goals/hooks";

export const Route = createFileRoute({
  component: RouteComponent,
});

function RouteComponent() {
  const { goalId } = Route.useParams();
  const { goal, isLoading, error } = useGoal(goalId);

  if (isLoading) {
    return (
      <>
        <PageHeader title="Goal Details" />
        <LoadingIndicator />
      </>
    );
  }

  if (error) {
    return (
      <>
        <PageHeader title="Goal Details" />
        <ErrorMessage title="Failed to load goal" error={error} />
      </>
    );
  }

  if (!goal) {
    return (
      <>
        <PageHeader title="Goal Details" />
        <ErrorMessage title="Goal not found" error={new Error("The requested goal could not be found.")} />
      </>
    );
  }

  return (
    <>
      <PageHeader title={goal.name} backLink={{ to: "/goals" }} />
      <GoalDetailsOverview goal={goal} />
      <GoalDialog />
    </>
  );
}
