import { ErrorMessage, PageHeader } from "~/components/blocks";
import { GoalDialog, GoalsList } from "~/features/goals/components";
import { useGoals } from "~/features/goals/hooks";

export const Route = createFileRoute({
  component: RouteComponent,
});

function RouteComponent() {
  const { goals, isLoading, error } = useGoals({ sortByPriority: true });

  if (error) {
    return (
      <>
        <PageHeader title="Goals" />
        <ErrorMessage title="Failed to load goals" error={error} />
      </>
    );
  }

  return (
    <>
      <PageHeader title="Goals" />
      <GoalsList goals={goals} isLoading={isLoading} />
      <GoalDialog />
    </>
  );
}
