import { Link } from "@tanstack/react-router";

import NavLink from "./nav-link";
import ProfileDropdown from "./profile-dropdown";

export default function Header() {
  return (
    <div className="bg-white">
      <div className="app-container flex justify-between gap-6">
        <div className="flex items-center gap-4">
          <Link to="/">
            <img src="/logo.webp" className="aspect-square h-11" />
          </Link>
          <h3 className="text-foreground text-xl leading-none font-bold">
            Finanze<span className="text-primary">.Pro</span>
          </h3>
        </div>

        <div className="flex">
          <NavLink to="/">Dashboard</NavLink>
          <NavLink to="/transactions">Transactions</NavLink>
          <NavLink to="/budgets">Budgets</NavLink>
          <NavLink to="/goals">Goals</NavLink>
          <NavLink to="/accounts">Accounts</NavLink>
          <NavLink to="/categories">Categories</NavLink>
        </div>

        <div className="flex items-center gap-4">
          <div className="flex flex-col items-end">
            <span className="text-foreground text-sm leading-5 font-semibold">Sergey</span>
            <span className="text-xs leading-4 text-gray-500">User</span>
          </div>

          <Link to="/" className="block">
            <img src="/user-placeholder.png" className="aspect-square h-11" />
          </Link>

          <ProfileDropdown />
        </div>
      </div>
    </div>
  );
}
